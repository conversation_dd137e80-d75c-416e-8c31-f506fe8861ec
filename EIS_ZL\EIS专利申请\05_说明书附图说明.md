# 说明书附图说明

## 附图清单

本专利申请包含以下附图：

- **图1**：系统整体架构图
- **图2**：DNB1101BB芯片连接电路图  
- **图3**：外部电流源电路详细图
- **图4**：EIS测试流程图
- **图5**：奈奎斯特曲线示例图
- **图6**：参数提取算法流程图
- **图7**：九档分组决策流程图
- **图8**：Modbus RTU通信时序图

## 各图详细说明

### 图1：系统整体架构图

**图1说明**：
展示了基于电化学阻抗谱的电池一致性筛选设备的整体系统架构，包括：

**主要组件**：
- 1-被测电池
- 2-DNB1101BB电化学阻抗测试芯片
- 3-STM32F103RCT6主控制器
- 4-外部电流源电路
- 5-串口显示屏
- 6-PC上位机
- 7-测试夹具

**连接关系**：
- 被测电池(1)通过测试夹具(7)连接到DNB1101BB芯片(2)
- 外部电流源(4)由DNB1101BB芯片控制
- STM32控制器(3)通过SPI接口与DNB1101BB芯片通信
- 串口屏(5)显示实时测试状态
- PC上位机(6)通过USB/串口与STM32通信

### 图2：DNB1101BB芯片连接电路图

**图2说明**：
详细展示了DNB1101BB芯片的引脚连接和外围电路，包括：

**关键连接**：
- 引脚15(VBAT)：电池正极连接
- 引脚6(VSS)：电池负极连接  
- 引脚16(VCHm)、引脚17(VCHg)：电压测量通道
- 引脚5(VCLm)、引脚4(VCLg)：电压测量参考
- 引脚3(VSW)：MOSFET驱动输出
- 引脚2(VDR)：MOSFET状态监控
- 引脚7(DIOBOTp/MOSI)、引脚8(DIOBOTn/SCK)：SPI通信
- 引脚10(MISO)：SPI数据输出

**外围器件**：
- C1：10μF去耦电容
- C2：100nF滤波电容
- R1：4.7kΩ上拉电阻
- R2、R3：SPI接口匹配电阻

### 图3：外部电流源电路详细图

**图3说明**：
展示了外部电流源电路的具体实现，包括：

**电路组成**：
- Q1：NMOS功率管(如PMV28UNEA)
- R_ext：功率电阻(20Ω/10Ω/6.67Ω/5Ω可选)
- D1：保护二极管
- C3：滤波电容

**工作原理**：
- VSW引脚输出PWM信号驱动MOSFET栅极
- MOSFET导通时，电流通过功率电阻流向电池
- VDR引脚监控MOSFET漏极电压状态
- 激励电流大小由电池电压和功率电阻决定

### 图4：EIS测试流程图

**图4说明**：
展示了完整的EIS测试流程，包括以下步骤：

**流程步骤**：
1. 初始化系统和设置测试参数
2. 设置频率序列(0.0075Hz-7800Hz)
3. 设置增益参数(1x/4x/16x)
4. 选择采样电阻值
5. 启动电流源激励
6. 测量复阻抗数据(实部+虚部)
7. 频率扫描完成判断
8. 数据处理和存储
9. 绘制奈奎斯特曲线
10. 参数提取和分组决策

### 图5：奈奎斯特曲线示例图

**图5说明**：
展示了典型的电池奈奎斯特曲线，包括：

**曲线特征**：
- 横轴：阻抗实部(Ω)
- 纵轴：阻抗虚部(Ω)  
- 高频区：欧姆阻抗Rs
- 中频区：极化阻抗半圆
- 低频区：瓦尔堡阻抗直线

**参数标注**：
- Rs：欧姆阻抗(实轴截距)
- Rp：极化阻抗(半圆直径)
- 频率标注：从高频到低频的变化趋势

### 图6：参数提取算法流程图

**图6说明**：
展示了从奈奎斯特曲线中提取多维参数的算法流程：

**算法步骤**：
1. 输入复阻抗数据
2. 数据预处理和滤波
3. 等效电路模型拟合
4. 参数优化计算
5. 提取Rs(欧姆阻抗)
6. 提取Rp(极化阻抗)  
7. 提取SEI膜阻抗
8. 提取瓦尔堡阻抗
9. 参数验证和输出

### 图7：九档分组决策流程图

**图7说明**：
展示了基于多维参数的智能分组决策流程：

**分组流程**：
1. 批量电池数据统计
2. 计算Rs中位值和标准差
3. 计算Rp中位值和标准差
4. 确定Rs三档阈值
5. 确定Rp三档阈值
6. 单电池Rs档位判定
7. 单电池Rp档位判定
8. 组合形成九档分组(1-1至3-3)
9. 输出分组结果

### 图8：Modbus RTU通信时序图

**图8说明**：
展示了设备与上位机之间的Modbus RTU通信时序：

**通信流程**：
1. 主机发送请求帧
2. 从机接收和解析
3. 从机执行相应操作
4. 从机发送响应帧
5. 主机接收响应数据

**帧格式**：
[设备地址][功能码][数据地址][数据长度][数据内容][CRC校验]

---

## 附图制作要求

### 图纸规格
- **图纸尺寸**：A4纸张(210mm×297mm)
- **图框边距**：上25mm，下25mm，左25mm，右15mm
- **线条粗细**：主要线条0.4mm，辅助线条0.2mm
- **字体大小**：标注文字不小于3.5mm

### 绘制标准
- **电路图**：符合GB/T 4728电气图用图形符号标准
- **流程图**：符合GB/T 1526流程图符号标准
- **尺寸标注**：清晰准确，单位统一
- **图例说明**：必要时添加图例和说明

### 图片质量
- **分辨率**：不低于300dpi
- **格式要求**：黑白线条图，避免灰度和彩色
- **清晰度**：线条清晰，文字可读
- **对比度**：黑白对比鲜明

---

## 附图文件准备

### 您需要准备的图片：

1. **实物照片**：
   - 测试设备整体照片
   - DNB1101BB芯片电路板照片
   - 测试夹具和连接线照片

2. **电路图**：
   - 完整的电路原理图
   - PCB布局图(如需要)

3. **软件界面**：
   - PC上位机软件界面截图
   - 串口屏显示界面照片

4. **测试结果**：
   - 实际测试的奈奎斯特曲线
   - 分组结果统计图表

### 图片处理建议：

1. **转换为线条图**：将照片转换为专利要求的黑白线条图
2. **添加标注**：在图上标注关键部件和连接关系
3. **统一编号**：按照附图说明中的编号标注各部件
4. **尺寸适配**：确保图片在A4纸上清晰可见

我已经为您准备了完整的附图说明文档，您只需要根据这个说明制作相应的图片即可。
