













数据手册–版本 2.0



DNB110xB
集成阻抗测量功能的单电芯管理芯片



特点
• 单电芯管理
• 电池电压范围: 1.9 - 5.5 V
• 电压测量精度:  ±2 mV
• 温度传感器精度:  ±2.5 K
• 可测量低内阻电池EIS（电化学阻抗谱）*
• EIS频率范围: 0.0075Hz  至 7800Hz*
• 菊花链中的最大IC数: 252
• 差分菊花链通信速度: 1 Mbps
• 可配置作为SPI桥接
• 支持芯片内部均衡和外部均衡

• 100个电芯系统的数据读取时间为: 3.5ms
• 两个独立的电压测量通道
• 集成过压、欠压、过温、欠温检测
• 具有稳定的热插拔性能
• 符合工业应用要求

*表示不适用于 DNB1100B
应用
电网储能系统
可再生能源存储系统
电信基站备用电源数据中心备用电源封装
概述
DNB110xB是一款专为工业储能系统应用而设计的单电芯管理 IC。该 IC  为每个单独的电池或一组并联电池提供多种传感器功能，包括电压测量、温度测量和电化学阻抗谱测量 (EIS)。通过差分菊花链，将电池测量信息传输到电池包控制器。链中的第一个 IC  将标准 SPI  信号转换为菊花链差分信号，反之亦然。

芯片内集成的温度传感器可用于电池温度测量。这样就不再需要外部热敏电阻，且每块电池都有温度监控覆盖。

基于自有专利技术，DNB110xB能够在很宽的频率范围内测量电池的交流阻抗。交流阻抗数据为电芯的安全、老化和性能提供了一种可视化途径。

编号	封装	尺寸
DNB110xB	HTSSOP20	6.5mm*4.4mm

有关更多详细信息，请参阅封装部分。































DNB110xB 数据手册	1 / 54

















































图 1 - DNB1101B 应用示例（12 节电池）





































































































DNB110xB 数据手册	2 / 54



















1.  目录

2.	订购信息......................................................................................................................................... 6

2.1	元件编号定义.......................................................................................................................... 6

3.	引脚信息......................................................................................................................................... 7

3.1	引脚图...................................................................................................................................... 7

3.2	引脚定义.................................................................................................................................. 7

4.	产品特性......................................................................................................................................... 9

4.1	最大可承受范围...................................................................................................................... 9

4.2	电气特性.................................................................................................................................. 9

5.	典型性能特征............................................................................................................................... 14

6.	芯片详细描述............................................................................................................................... 15

6.1	概述........................................................................................................................................ 15

6.2	功能框图................................................................................................................................ 15

6.3	功能模式................................................................................................................................ 15

6.4	总线活动检测器 (BADET)..................................................................................................... 16

6.5	操作模式................................................................................................................................ 16

6.6	有限状态机 (FSM) ................................................................................................................ 16

6.7	内部电源调节器 (ISR) .......................................................................................................... 17

6.7.1	概述............................................................................................................................... 17

6.7.2	欠电触发和断电触发................................................................................................... 17

6.8	内部振荡器............................................................................................................................ 18

6.9	通讯接口................................................................................................................................ 18

6.9.1	概述............................................................................................................................... 18

6.9.2	测量模式下的 DNB110xB ............................................................................................ 18

6.9.3	SPI  模式下的 DNB110xB ............................................................................................. 18

6.9.4	DIO  物理层................................................................................................................... 19

6.9.5	SPI  物理层 .................................................................................................................... 19

6.9.6	通讯协议....................................................................................................................... 20

6.10	存储体系................................................................................................................................ 23

6.10.1	寄存器........................................................................................................................... 23

6.10.2	MTP（多次可编程存储器）........................................................................................ 23



DNB110xB 数据手册	3 / 54















6.10.3	将 ID 和设置写入 MTP ................................................................................................ 24

6.11	电压测量 (VM)...................................................................................................................... 24

6.11.1	概述............................................................................................................................... 24

6.11.2	等间隔/非等间隔.......................................................................................................... 24

6.11.3	VM  输入滤波器特性 ................................................................................................... 25

6.12	温度测量(TM) ........................................................................................................................ 25

6.12.1	自热补偿(SHC) .............................................................................................................. 25

6.13 均衡........................................................................................................................................ 26

6.13.1	内部电流源................................................................................................................... 26

6.13.2	外部电流源................................................................................................................... 26

6.14	阻抗测量 (ZM) ...................................................................................................................... 27

6.14.1	概述............................................................................................................................... 27

6.14.2	测量............................................................................................................................... 28

6.14.3	低频噪声抑制（LFNS） ............................................................................................... 28

6.15	芯片启动................................................................................................................................ 29

6.16	诊断........................................................................................................................................ 29

6.16.1	服务请求....................................................................................................................... 29

6.16.2	MTP 影子寄存器的 CRC 校验 ...................................................................................... 30

6.16.3	“设置”寄存器的硬件校验值 (CRC) ........................................................................ 30

7.	指令列表....................................................................................................................................... 31

7.1	概述........................................................................................................................................ 31

7.2	指令详情................................................................................................................................ 31

7.2.1	枚举 Enumerate (00h)................................................................................................... 31

7.2.2	初始化 Initialisation	(01h).......................................................................................... 32

7.2.3	设置 MTP 锁存码 SetMTPLockKey（02h）................................................................. 32

7.2.4	设置电压阈值 SetThVolt	(03h) .................................................................................. 32

7.2.5	设置温度阈值 SetThTemp	(04h) ............................................................................... 33

7.2.6	设置阻抗测量电流 SetZMCurr	(06h) ........................................................................ 33

7.2.7	设置阻抗测量频率 SetZMFreq	(07h) ........................................................................ 34

7.2.8	设置均衡电流 SetBalCurr	(08h) ................................................................................ 34

7.2.9	设置均衡电压 SetBalVolt	(09h) ................................................................................. 35

7.2.10	设置服务请求掩码位 SetSRMask (0Ah)....................................................................... 35

DNB110xB 数据手册	4 / 54















7.2.11	设置模式 SetMode (0Bh).............................................................................................. 35

7.2.12	设置强制报错 SetForcedError (0Ch) ............................................................................ 36

7.2.13	获取状态 GetStatus(0Dh)............................................................................................ 37

7.2.14	获取数据 GetData (0Eh) ............................................................................................... 40

7.2.15	设置寄存器块 SetRegBank (0Fh).................................................................................. 41

8.	SPI 时序......................................................................................................................................... 41

9.	应用............................................................................................................................................... 42

9.1	原理图和元器件选择............................................................................................................ 42

9.1.1	DNB110xB 在 SPI 模式.................................................................................................. 42

9.1.2	测量模式下的带阻抗测量的 DNB110xB ..................................................................... 43

9.1.3	测量模式下的不带阻抗测量的 DNB110xB ................................................................. 44

9.1.4	相同电压域的 IC 处理.................................................................................................. 45

10.   寄存器列表................................................................................................................................... 46

11.   封装............................................................................................................................................... 50

12.   参考文献....................................................................................................................................... 52

13.   修订历史....................................................................................................................................... 53

14.   法律信息....................................................................................................................................... 54

14.1	免责声明................................................................................................................................ 54

14.2	商标........................................................................................................................................ 54





























































DNB110xB 数据手册	5 / 54















2.  订购信息

2.1   元件编号定义





DNB110xB y HS TR







表1–IC版本


代码	元件号	描述

x	DNB1101B	全功能
	DNB1100B	无 EIS  功能







表 2 – MTP 版本


代码	符号	描述

y	A	全功能
	B	无安全模式
	HS	包装后缀
	TR	卷带标识符







































































DNB110xB 数据手册	6 / 54















3.  引脚信息

3.1   引脚图









































图2. DNB110xB (封装: HTSSOP20)



3.2   引脚定义

表3–引脚定义


引脚号	引脚名	类型	描述
1	NU	Not Used	建议接地
2	VDR	Input	外部 MOSFET  漏极电压监视
3	VSW	Output	外部 MOSFET  栅极驱动器输出
4	VCLg	Input	电池负极电压检测（辅）
5	VCLm	Input	电池负极电压检测（主）
6	VSS	Ground	电源地
7	DIOBOTp/
MOSI	Input/Output	DIO  底部正极引脚（参考：VSS）
用作 SPI  接口时为主输出从输入（MOSI）
8	DIOBOTn/
SCK	Input/Output	DIO  底部负极引脚（参考：VSS）
SCK  用作 SPI  接口时来自控制器的时钟输入
9	SPI_En	Input	SPI 模式使能，高电平时为 SPI 模式，低电平为测量模式
10	MISO	Output	用作 SPI  接口时的主输入从输出 (MISO)
11	NU	Not Used	建议接地
12	NU	Not Used	建议接地
13	DIOTOPn	Input/Output	DIO  顶部负极引脚（参考：VBAT）
14	DIOTOPp	Input/Output	DIO  顶部正极引脚（参考：VBAT）
15	VBAT	Power	电源输入
16	VCHm	Input	正极电池电压监测（主）
17	VCHg	Input	正极电池电压监测（辅）
18	VHP	Input	ZM ADC  分频器的输入



DNB110xB 数据手册	7 / 54















19	VBAT_FIL	Power	内部电源节点
20	NU	Not Used	接地
N/A	E-PAD	Ground	裸露芯片焊盘，接地

































































































































DNB110xB 数据手册	8 / 54















4.  产品特性

4.1   最大可承受范围

除非另有说明，所有电压均相对于地。超过这些额定值可能会导致芯片发生故障或永久性损坏。

表4-ESD



名称	描述	值	单位
ESD_HBM	ESD  保护—  人体放电模式	±2000	V
ESD_CDM	ESD  保护—  组件充电模式	±500	V
ESD_CDM2	ESD  保护— 组件充电模式拐角 pin	±750	V



表5-绝对最大额定值



参数	描述	最小值	典型值	最大值	单位
VSUPPLY	电池电压绝对最大额定值。 VBAT  引脚上的电压	-0.3		7.2	V
VDIOBOTX	底部 DIO  引脚电压、MOSI、SCK	-7.2		7.2	V

VDIOBOT_CM	底部 DIO  引脚共模电压
(VDIOBOTX_DIFF = 0 V)	
-6.0		
6.0	
V
VDIOTOPX	顶部 DIO  引脚电压	Vbat-7.2		Vbat+7.2	V
VDIOTOP_CM	顶部 DIO 引脚共模电压(VDIOBOTX_DIFF = 0 V)	Vbat-6.0		Vbat+6.0	V
VCHm/g	VCHm  和 VCHg  引脚电压	-0.3		Vbat+0.3	V
VVDR	VDR 引脚电压	-0.3		Vbat+0.3	V
VSPI_en	SPI_en 引脚电压	-0.3		5.5	V
VVCLg, VVCLm	VCLx 引脚电压	-0.3		0.3	V
VVHP	VHP 引脚电压	-0.3		Vbat+0.3	V
VBAT_FIL	VBAT_FIL 引脚电压	-0.3		7.2	V

TDIE	
结温范围	
-40		
105	
℃



4.2   电气特性

表 6–直流特性
除非另有说明，否则典型值是在 VBAT = VCHm/g = 3.7 V、TAMB = 25°C  室温条件下提供的。

参数	描述	状态	最小值	典型值	最大值	单位
VSUPPLY(FS)	电源电压 -  正常工作范围		1.9		5.5	V
VBROWN[1]	欠电状态阈值电平	VBAT  下降	1.7		1.85	V
VBLACK[2]	断电阈值	VBAT  下降	1.3	1.4	1.5	V
VPOR	上电复位电平	VBAT  上升	1.91	2.05	2.19	V

ISLEEP_1	
睡眠模式电流消耗（测量模式）	-40 ≤ T ≤ 60°C
1.9 < VBAT < 5.5 V
SPI_EN = low		
32	
50	
µA
ISLEEP_2	睡眠模式电流消耗（测量模式）	-40 ≤ T ≤105°C		32	65	µA

DNB110xB 数据手册	9 / 54















		1.9 < VBAT < 5.5 V
SPI_EN = low				

ISLEEP_SPI_1	
睡眠模式电流消耗（SPI  模式）	-40 ≤ T ≤ 60°C
1.9 < VBAT < 5.5 V
SPI_EN = high		
100	
150	
µA

ISLEEP_SPI_2	
睡眠模式电流消耗（SPI  模式）	-40 ≤ T ≤ 105°C
1.9 < VBAT < 5.5 V
SPI_EN = high		
100	
200	
µA

ISLEEP_VAR	睡眠模式电流消耗 IC  之间的差
异（测量模式）	相等的VBAT，
最大值温差为15°C	
-12.5		
12.5	
µA

ISTBY	
待机模式电流消耗（测量模式）	-40 ≤ T ≤ 105°C
2.7 < VBAT < 5.5 V
SPI_EN = low		
350	
565	
µA

ISTBY_SPI	
待机模式电流消耗（SPI 模式）	-40 ≤ T ≤ 105°C
2.7 < VBAT < 5.5 V
SPI_EN = high		
415	
705	
µA

INORMAL	
正常模式电流消耗	-40 ≤ T ≤ 105°C
1.9 < VBAT < 5.5 V
通讯过程中		
17	
20	
mA

INORMAL_VAR1	
正常模式下 IC 之间的电流消耗差异	相同的IC温度
2.5 < VBAT < 5.5 V
(25℃)	
-500		
500	
µA

INORMAL_VAR2	正常模式下 IC 之间的电流消耗
差异	-40 ≤ T ≤ 105°C
2.5 < VBAT < 5.5 V	
-1		
1	
mA

ISPI	
SPI-正常模式电流消耗	-40 ≤ T ≤ 105°C
2.7 < VBAT < 5.5 V		
8	
13	
mA

ISELF-TEST	
自检模式电流消耗	-40 ≤ T ≤ 105°C
1.9 < VBAT < 5.5 V
通讯过程中		
17	
20	
mA

ISAFE	
安全模式电流消耗			
17	
20	
mA
RVSW_CHARGE	VSW  引脚源电阻		200	800	3000	Ω
RVSW_DISCHARGE	VSW  引脚灌电阻		50	120	390	Ω







表 7 -电压测量特性
除非另有说明，否则典型值是在 VBAT = VCHm/g = 3.7 V、TAMB = 25°C  室温条件下提供的。

参数	描述	状态	最小值	典型值	最大值	单位

VBAT	电池电压测量范围。
VBAT、VCHm/g  引脚上的电
压。		
1.9		
5.5	
V



VMACC	


电池电压测量精度	2.0 ≤ VBAT ≤ 5.0 V -20 ≤ T ≤ 60°C
-40 ≤ T ≤ 85°C	
-2
-4		
2
4	
mV
mV
		1.9 ≤ VBAT ≤ 5.5 V
-40 ≤ T ≤ 105°C	
-7.5		
7.5	
mV

DNB110xB 数据手册	10 / 54















NVM	电压测量 ADC  分辨率			14		Bits

VMINT	VM  积分时间
（采样率）		4.096
(244.1)		1048.6
(0.95)	ms
Hz
SSYN	测量同步性				0.2	ms







表 8 -阻抗测量特性[3]

除非另有说明，否则典型值是在 VBAT = VCHm/g = 3.7 V、TAMB = 25°C  室温条件下提供的。以下规格可根
据第 9.1  节中所述的推荐元器件实现。

参数	描述	状态	最小值	典型值	最大值	单位


FRIMP	

阻抗测量的频率范围	
Gain G=1
Gain G=4 or G=16 (带
22uF VHP  电容器)	
0.0075
10		
7812.5
7812.5	

Hz
VRIMP	阻抗测量的电压范围		2.5		5.0	V
TRIMP	阻抗测量的温度范围		-40		105	°C

NZM1	阻抗测量期间的电压噪声电平
(3σ)	Gain = 1
LFNS = ON		
见图 3		
μV

NZM4	阻抗测量期间的电压噪声电平
(3σ)	Gain = 4
LFNS = ON		
见图 4		
μV
NZM16	阻抗测量期间的电压噪声电平
(3σ)	Gain = 16
LFNS = ON		见图 5		μV

TAMP	阻抗测量的系统幅值误差（真
值）	外接功率电阻值误差
范围<1%		
见图 6		
%

TANG	阻抗测量的系统相位误差（真
值）	外接功率电阻值误差
范围<1%		
见图 7		
°

ZM_int	阻抗测量积分时间
（采样率）		1048.6
(0.95)			ms
(Hz)
Zdev	激励频率的偏差	通讯状态：连续	-1		1	%



表 9 –电池均衡特性



参数	描述	状态	最小值	典型值	最大值	单位
VBAL	电池均衡电压范围		2.5		5.5	V
VBAL_T	基于电压的均衡：阈值容差	见 VMACC   参数
TBAL	电池均衡时间		134		34226	s
TBAL_TOL	电池均衡时间容差		-2.5		+2.5	%
IINT	内部均衡电流范围		5		205[4]	mA



IINT_TOL	


内部均衡电流值容差(参考值 : 205mA )	-10°C < T ≤ 85°C
2.5 < Vbal ≤ 5.5V	
-2		
+2	
%
		-40°C ≤ T ≤ -10°C
85°C < T ≤ 105°C
2.5 < Vbal ≤ 5.5V	
-4		
+4	
%

DNB110xB 数据手册	11 / 54















表 10 –芯片温度测量特性



参数	描述	状态	最小值	典型值	最大值	单位

VDTS	芯片温度测量（主/辅）电源电压
范围		
1.9		
5.5	
V

FDTS_GUARD	芯片辅 DTS 测量积分时间
（采样率）			8.2
(122)		ms
(Hz)

FDTS_MAIN	芯片主 DTS 测量积分时间
（采样率）			16
(62)		ms
(Hz)

RDTS_GUARD	
芯片辅 DTS 测量分辨率			
0.0625		
℃

RDTS_MAIN	
芯片主 DTS 测量分辨率			
0.250		
℃

TACC	
芯片辅 DTS 测量精度	-10 ≤ T < 85℃
-40 ≤ T < 105℃	-2.5
-3.5		2.5
3.5	
℃



表11 -通讯特性

除非另有说明，否则在 VBAT = VCHm/g = 3.7 V、 TAMB = 25°C  室温条件下提供典型值。

参数	描述	状态	最小值	典型值	最大值	单位
SPDDC	菊花链通信速率			1		Mbps
SCKCLK	SCK  时钟		0.999	1	1.001	MHz
DrvDIS	
两个差分通信IC之间的驱动距离	
带隔离变压器			
200	
cm

Tcom(100)	100串电池串联时，传输所有的DNB110xB
数据信息到主控端所需时间			
3.5		
ms

Tcom(250)	250串电池串联时，传输所有的DNB110xB
数据信息到主控端所需时间			
8.5		
ms
Nnum	单串IC支持拓展数量（测量模式）				250	



表 12 - MTP



参数	描述	状态	最小值	典型值	最大值	单位
VMTP	MTP  写入的电源电压范围		1.9[5]			V

NMTP	
MTP的可写入次数（无SR置位条件下）				
200	

NMTP_MAX	MTP最大可写入次数（超过该次数，写入
会被禁止）				
255	

NMTP_LOCK	使用错误的密钥对MTP的最大允许写入次
数				
15	
TWRITE	写操作所需的时间		50			ms











DNB110xB 数据手册	12 / 54















表13 -诊断



参数	描述	状态	最小值	典型值	最大值	单位
RVOL	欠压/过压可设置范围		1.9		5.5	V
VOV /VUV	过压/欠压检测阈值容差	见 VMACC   参数
RDTS	欠温/过温可设置范围		
-35		
105	
℃
TMAX_DIE	芯片告警的最高结温				
105	
℃
TMIN_DIE	芯片告警的最低结温		
-40			
℃
TOT/TUT	过温/欠温检测阈值的容差（辅）	
见 TACC_GUARD   参数
VDRH	VDR为高的阈值（不低于）		Vbat-
0.6		Vbat-
0.4	V
VDRL	VDR为低的阈值（不高于）		0.45		0.55	V

注释 [1]：欠电状态：电源电压过低。芯片的寄存器配置的设置保持不变。无法保证通信和测量的准确性。IC的寄存器中将设置一个欠电标志。
注释[2]：掉电状态：电压下降到IC无法工作的状态。芯片会重置为未配置状态，需要通过枚举和初始化来重新启动（在电压恢复后）。设备已重置为出厂设置。必须通过枚举和初始化过程重新启动设备。
注释[3]：不适用于 DNB1100B

注释[4]：最大电流可能受限于环境温度。

注释[5]：MTP写入时，需要确保芯片不处于欠电状态。





























































DNB110xB 数据手册	13 / 54















5.  典型性能特征





























图 3 - ZM 噪声@增益 = 1	图 4 - ZM 噪声@增益 = 4





























图 5 - ZM 噪声@增益 = 16	图 6 – ZM 准确度 – 幅值误差































图 7 – ZM 准确度 – 相位误差



































DNB110xB 数据手册	14 / 54























VBAT	ISR

ESD + Vbat filter

VSS
References
Vdda
Guard
vddd	vdd_fsm
Vdda
Main
















VCHm

VCLm

VCHg

VCLg



VHP
DIOBOTp (MOSI) DIOBOTn (SCK) VSW (MISO)

DIOTOPp

DIOTOPn

DIOBOTp

DIOBOTn



























VDR	VSW	VBAT	VSS



图 8 - DNB110xB 简化框图（ZM-ADC 不适用于DNB1100B）




6.  芯片详细描述

6.1   概述

DNB110xB  是一款单节电池监控 IC，能够测量电池单元的电压 (VM)、温度 (TM)  和阻抗 (ZM)。它还包含均衡功能。它可以配置为两种不同的功能模式：测量模式和 SPI  模式。


6.2   功能框图

功能框图如图 8  所示。IC  的主要模块有：ISR（内部电源稳压器）、主辅测量通道（VM  和 TM）、ZM  测量通道、FSM（有限状态机）、MOSFET  栅极驱动器、内
部电流 DAC（CDAC）、振荡器、内核、MTP 和 DIO 通信接口。


6.3   功能模式

当 IC  上电且 SPI_EN  引脚接地时，处于测量模式。在此模式下，IC  可用于执行均衡和所有测量（VM、TM、ZM）。
当 IC  上电且 SPI_EN  引脚拉高时，IC 处于 SPI  模式。在此模式下，IC  用作 SPI  接口到 DIO  接口的转换器，此时底端 DIO 接口(DIOBOT)转为 SPI 接口而顶端(DIOTOP)仍然为 DIO 接口。需要注意的是，在 SPI 模式下用户仍然可以测量芯片温度 (TM)，但是电池均衡功能，电压 (VM)  和阻抗 (ZM)  测量不可用。



DNB110xB 数据手册	15 / 54
















6.4   总线活动检测器

(BADET)
自检模式
自检模式用于检查在不中断正常操作的情况下无


如果在 DIO  总线上检测到差分信号（IC  在测量模式下）或在 DIOBOTn/SCK  引脚上检测到单端信号（IC  在 SPI  模式下），总线活动检测器会将 IC  从睡眠或待机模式中唤醒。如果没有收到有效命令，IC  将在
1 秒后返回睡眠或待机模式，具体取决于初始状态。通
信方向由接收器探测到的首次信号决定。
法检查的安全机制。进入此模式时，通过在安全相关比较器的输入端强制执行某些条件（例如，在过压比较器的输入端强制执行故障）来模拟几种错误条件。



安全模式





6.5   操作模式

睡眠模式
除总线活动检测器 (BADET)  和有限状态机(FSM)  外，所有测量和通信电路以及振荡器和所有数字电路都关闭。如果用户已将 ID 写入 MTP，IC将保持其 ID  地址，并监测 IO 口上的活动。初始上电后，IC  处于睡眠模式。收到 BADET 触发信号并检测到有效命令，IC  将进入正常模式或 SPI-正常模式，具体取决于 SPI_EN  引脚的配置（见表 15）。如果未检测到有效命令，IC  将在一秒钟后自动返回睡眠模式。



正常模式
所有测量和通信电路都处于激活状态。阻抗测量和电池均衡功能只能在这种模式下进行。能够提供完整的诊断（例如冗余测量的采集值的比较）。



SPI-正常模式
只有当 DNB110xB 在电池包控制器和电池组之间作为 SPI 桥接芯片时，才会处于此模式。在此模式下，某些功能（如电压测量、阻抗测量）不可用。



待机模式
所有测量电路、通信电路和振荡器都关闭。 IC保持其所有设置并监测 IO  上的活动。所有寄存器都保持其最新的设置。这将极大减少 IC 初始化时间。
当检测到可能导致电池完全放电的情况时，IC  将进入安全模式。在安全模式下，除内部电流源和外部MOSFET  驱动器外，所有功能都保持激活状态。阻抗测量功能不可用。
有关操作模式的更多信息，请参见表 42。





6.6   有限状态机 (FSM)

DNB110xB  的数字部分不包含 MCU、软件或固件。取而代之的是，状态机以及专用信号处理机制。这简化
了功能安全设计并使其更加稳健。状态机的详细框图
如图 9 所示。









DNB110xB 数据手册	16 / 54





















































































图 9 –有限状态机 (FSM)




6.7   内部电源调节器 (ISR)

6.7.1   概述

内部电源由多个低压差稳压器 (LDO)  组成，每个稳压器都有自己的电压窗检测器。如果一个或多个
表14 – IC 状态与 VBAT

LDO   在 其 电 压 窗 之 外 ， 则 获 取 状 态 GetStatus
（SrvReqData）中的 LDOOoR 服务请求标志被置位，IC

如果电压低于 1.5 V，IC 无法保存寄存器的配置值，

将进入安全模式。可以从寄存器 ICDiagnostics（地址：	并且会产生 POR  信号。则会触发断电。 IC  将进入睡


0x18）中获取有关触发标志的特定 LDO  的信息。

6.7.2   欠电触发和断电触发

IC  的工作电压范围在 1.9 V  和 5.5 V  之间。如果电压低于 1.85 V，则会触发欠电。所有电源都处于活动状态，但振荡器将停止工作。这意味着 IC  将停止工作并且无法进行测量。但是，由于电源保持启用状态，因此寄存器中的配置信息将保留。从欠电状态中恢复后，它将继续运行而无需重新配置电池包寄存器。
眠模式并丢失除存储在 MTP  中的设置外的所有设置。当电池电压升至 Vpor  以上时，总线活动检测器执行
正常唤醒。



DNB110xB 数据手册	17 / 54



















Vnpor
Vbrown

Vblack

Vbat

POR








1








0
Reset flag








1








0



Brownout flag	0	1	0	1	0

Re-enum,
state to normal
Re-enum,
state to normal

DIO, ADCs,
DTS, etc
0	1
0
1
0
1



图 10– IC 状态与 VBAT

图 11-容错环形拓扑

6.8   内部振荡器

每个 IC  都集成了一个本地振荡器，为 IC  中的所有数字模块提供时钟。振荡器与来自通信接口模块的时钟同步。当 IC  配置为 SPI  模式时，时钟直接来自SCK  引脚。在测量模式下，时钟来自 DIO  接口，它利用曼彻斯特编码来获得没有直流分量的自时钟信号。

6.9   通讯接口

6.9.1   概述

电池包控制器（主控器）通过 SPI  网关（即配置为 SPI  模式的 DNB110xB）与整个 IC  链通信。每个 IC在两端都有一个通信接口：顶端（DIOTOP）和底端（DIOBOT/SPI）。 SPI  网关使用底端作为 SPI  接口，在顶端使用两线差分菊花链 (DIO)。如果 IC  配置为测量模式，它将使用两端的差分 DIO  接口。
图 11  是带有容错能力的环形拓扑。在这种方法中，电池包控制器可以分别从底部或顶部与 IC  链进行通信。通过增加冗余，使得当整个 IC 链中出现一个通信节点中断时，主控可以分别从顶部和底部来进行通信，增加了一定的容错能力。
图 12  是只有一个 SPI  网关和通过底部接口进行通信的基本通信拓扑。也可以仅通过顶部接口进行通信。




















图 12 -基本通信拓扑




6.9.2   测量模式下的 DNB110xB

将 SPI_EN 引脚设置为低电平，IC 将从睡眠模式启动并进入正常测量模式，此时 IC 有最完整的功能（包括 VM、TM、ZM 以及均衡功能）。有关状态转换的更多详细信息，请参见图 9。原理图和元器件选择在 9.1章节中介绍。


6.9.3   SPI  模式下的 DNB110xB

将 SPI_EN 引脚设置为高电平，IC  将从睡眠模式启动并进入 SPI  正常模式，充当 SPI  网关。有关状态转换的更多详细信息，请参见图 9。原理图和元器件选择在 9.1 章节中介绍。



DNB110xB 数据手册	18 / 54















表 15 - SPI_EN 引脚配置

SPI_EN  电平	功能模式	底部接口	顶端接口
GND	测量模式	DIO	DIO
VCC	SPI  模式	SPI	DIO

6.9.5   SPI  物理层

图 15 是 SPI  接口的简化示意图。







6.9.4   DIO  物理层

图 13  是差分通信接口 (DIO)  的简化示意图。每个 DIO  引脚都用 200  Ω 电阻端接。底部和顶部 DIO的终端电阻分别以 VSS（接地）和 VBAT（电源）为参考。差分信号输入时，等效端口阻抗为 400Ω，而共模信号输入时，等效端口阻抗为 100Ω。

RxBot

IC (N+1)




VSS



VBAT
200Ω	200Ω

DIOBOTp	DIOBOTn

DIOTOPp	DIOTOPn

200Ω	200Ω


RxTop
图 15- SPI 模式下的 DNB110xB；简化应用

MISO  引脚具有开漏输出，因此需要一个上拉电阻。
DIOBOTn/MOSI  和 DIOBOTp/SCK  输入端接 200  Ω电阻。这些输入的阈值电平约为 200 mV，因此如果MCU  以典型的 3.3 V  驱动它们，则它们需要外部电阻

IC (N)
器。有关应用和元器件选择的更多信息，请参见 9.1 章
节。

RxBot


200Ω	200Ω

VSS



DIOBOTp	DIOBOTn

图 13- DNB110xB DIO 物理层

图 14 是两个 IC 之间的通信链路的简化模型，包括电缆的阻抗。



Rdrive	DIOTOPp	DIOBOTp	Rdrive



Vdrive	200Ω	200Ω	Vdrive



VBAT	Z0	VSS

Vdrive	Vdrive

200Ω	200Ω



Rdrive	DIOTOPn	DIOBOTn	Rdrive



IC (n)	Cable	IC (n+1)



图 14 - 通信链路 (DIO) 的简化模型















DNB110xB 数据手册	19 / 54















































图 16 -基本通讯帧结构图







6.9.6   通讯协议	ID


6.9.6.1   介绍
DNB110xB  使用菊花链通信接口交换数据。通信速度通常为 1 Mbps (SCKCLK  = 1 MHz)。 DIO  使用两条线进行双向通信（全双工），而 SPI  接口使用典型的 3线连接（MISO、MOSI  和 SCK）。图 16  是与 IC  链（包括 SPI  网关）进行通信的基本概念。 电池包控制器(PC)  发送一个在链中传播的命令，同时接收以相反方
每个 IC  必须有自己的唯一 ID（地址）。然后，主控 器 可 以 寻 址 链 中 的 每 个 单 独 IC 。  ID   与NrOfICsInChain[8]  一起确定了每个 IC  在发送同步（广播）命令时将等待的时钟周期数。
有关命令同步执行的更多详细信息，请参阅章节
6.9.6.5。


向返回的确认帧。每次通信都必须以前导码启动，接着是一个 32 位命令和 N*32 个时钟信息，用于从链中筛选出确认帧。在主机收到所有确认帧后，时钟应该持续以保持本地振荡器同步。在这个阶段从链中得出的数据是没有意义的，应该被丢弃。


6.9.6.2   前导码
前导码用于本地振荡器与输入时钟同步。它由许多“0”组成，并以四个“1”结尾。如果 IC  处于睡眠或待机模式，发送前导码将触发 BADET，因此它将启动 IC  的唤醒阶段。


6.9.6.3   命令
每个命令由 ID、命令类型、命令数据和 CRC  字段组成。

表 16 –命令帧详细信息

数据头	有效荷载	CRC
ID
[7:0]	命令类型
[3:0]	命令数据
[15:0]	ID
[3:0]
表 17 – ID 概览







DNB110xB 数据手册	20 / 54















命令类型
有两种不同类型的命令：
• “设置”命令
• “获取”命令
“设置”命令应视为“写入”，而“获取”命令应视为






表 18 - 确认详情

“读取”。更多详情请参阅章节 7.1。

命令数据
这个 16 位宽的字段保存有效载荷数据。更多详情

请参阅章节 7.2。



ID

CRC
参阅章节 6.9.6.7。



6.9.6.4   两次发送命令
所有“设置”命令在执行之前必须发送两次。第二个“设置”命令必须在 1  秒内跟随第一个。这种方法将防止意外执行命令，例如来自通信线上的干扰。在前一个命令之后超过 1  秒收到的任何命令都将被视为新命令。只要满足 1  秒的时序规范，就可以在两次迭代之间发送一个或多个“获取”命令。
服务请求（CmdErr  标志）将通过以下方式置位：
•	命令不匹配：两个不同的连续“设置”命令在 1
秒内到达。




6.9.6.5   同步命令执行
每个设备在收到命令后，会等待直到链中的最后一个 IC  也收到同步命令，然后再执行它。因此，必须在每个设备中设置链中 IC  的数量（NrOfICsInChain）。



6.9.6.6   确认帧
确认帧为 32  位宽（MSB  在前），它包括：
•	8 位地址(ID)
•	4 位 ACK 半字节
•	16 位确认数据
•	4 位 CRC
与命令帧相同（见 6.9.6.3 章节）。



ACK  半字节
一组 4 位用于反映 IC  的内部状态。4 位 ACK  和
16  位确认数据的含义都取决于 ACK  半字节 (ACK[3])的 MSB  的值。如果 ACK[3]  位未置位，则表示已检测到接收到的命令有问题或 IC  已检测到事件（例如：过压）并且它会发送服务请求 (SR)  信息，而不管用户请求的数据是什么。在这种情况下，该命令将被忽略。如果命令是几个所谓的优先级命令之一，它总是被执行，并且在服务请求的情况下也是如此。有关服务请求处理的更多信息在章节 6.16.1。
IC 使用从 0 到 3 的循环计数器 (RSC)  。如果测量新数据可用，则循环计数器刷新该数据。













DNB110xB 数据手册	21 / 54

















表 19 - 确认帧详情； ACK 半字节（X-无关）



ACK

[3]	
ACK

[2]	
ACK

[1]	
ACK

[0]	注释
0	0	0	0	非法命令（例如：错误的寄存
器）。
IC  未执行的命令
0	X	X	1	IC  的服务请求。有关何时以及如何使用此确认帧的详细信息，请参阅第 6.16.1  章。命令被忽略。
0	X	1	X	IC  未寻址；命令被忽略
0	1	X	X	接收到的命令有 CRC  错误并被
忽略
1	0	X	0	设置确认，第一次发送命令（未
执行）。
1	0	X	1	设置确认，第二次发送命令（执
行）。
1	1	X	0	设置确认，第一次发送命令（未执行）。待处理的服务请求（参见第 6.16.1  章）。
1	1	X	1	设置确认，第二次发送命令（执行）。待处理的服务请求（参见第6.16.1  章）。
1	0	RSC[1:0]	获取命令的确认帧。 RSC  是循
环计数器
1	1	RSC[1:0]	获取命令的确认帧。 RSC  是循环计数器。待处理的服务请求（参见第 6.16.1  章）。













图 17 –简化的通信接口状态



在 IC  唤醒进入 IDLE  后，它将等待检测八个连续的零。然后再等待八个时钟周期后，开始向下一个 IC发送前导码。在此阶段，IC  将始终向下一个 IC  发送零，而与输入端接收到的符号无关。当 IC  检测到前导码结束标记（至少 60 个“0”以后再 4 个“1”）时，它将在其另一个通信 DIO  端口上重新生成接收到的数据。
从确认状态转移可以通过两种方式完成：以“0000”标记的形式接收确认帧结束标记，或者未检测到四个
连续的时钟沿。在此之后，通信接口将转换到 IDLE  状态。
如果通信接口处于前导码或命令状态并且丢失超
过 36 个连续时钟沿，那么它将转换到 IDLE  状态。
当 100ms  内没有通信启动时，通信接口将转换为静音（MUTE）状态。在这种状态下，接收器和发送器


CRC
参阅 6.9.6.7 章节。


6.9.6.7   通讯 CRC
错误检测是通过 4  位 CRC  完成的。 4  阶 CRC-4- ITU  多项式 (x^4 + x^3 + 1)  用于 CRC 校验。


6.9.6.8   通讯接口状态
通信接口有四种执行状态，对应于一次传输的这些部件（如图 16  所示）：前导码(Preamble)、命令帧(Command)、确认帧(Confirmation)和空闲期(IDLE)。这四种状态也可以在图 17  的状态图中找到。在正常操作中，通信接口将不断循环通过 空闲期 ->  前导码 ->命令帧 ->  确认帧。
都被禁用。再过 100ms  后，通信接口将转换到无声（SILENT）状态，此时 BADET 将在顶部 DIO 和底部 DIO上启用。在 SILENT  状态下，IC  将等待这两边 DIO  端口的活动 (如果 IC 处于 SPI 模式，则只有底部 DIO)。


6.9.6.9   容错环形拓扑
如果链中两个 IC  之间的连接断开，可以对其进行编程以拆分总线并使用备选的数据流（双链配置/容错系统）。为了做到这一点，SplitBus  位必须在断开连接旁边的两个 IC  中设置为 1。这将禁用通过断开的连接传输命令。图 18  是 ID=2  和 ID=3  的 IC  之间的断链示例。在这种情况下，在两个芯片中设置“SplitBus”将导致它们都不再传递命令，从而终止两个独立的 IC链。



DNB110xB 数据手册	22 / 54















表 20 -命令的传输取决于 SplitBus 和 ID（寄存器:‘Initialise

(01h)’）

标志位	ID = 0	ID <> 0
SplitBus[1]=0	失能	使能
SplitBus[1]=1	失能	失能

[CMD]



0h 1h 2h 3h

Bank 0
16 bits





Ch Dh Eh

Fh





RW





BankAddress













图 18- 断链和“SplitBus”
图 19–存储体系

DNB110xB  总共使用七个内存块，分为三个不同的部分，用于不同的用途。

Bank 0 (0h)：命令寄存器组
它是 IC  正常运行期间电池包控制器使用最广泛的块。它用于配置 IC  以及读取所有测量数据。



此功能还可用于将长链拆分为两个较短的链。这	Bank 123  到 127（7Bh  到 7Fh）：影子寄存器

减少了在整个链上传输所需的时间。




6.10 存储体系

6.10.1 寄存器

寄存器块用于存储设置，数据以及内部寄存器值。用户可以使用“SetRegBank”命令在块之间切换。每个存储块由 15  行 16  位寄存器组成。可以使用命令中的 4  位“命令类型”字段来寻址库中的每一行。每个块中最多包含 15*16=240  位。每个存储块中的 0Fh 地址，定义为“SetRegBank”命令（图 19）。

影子寄存器包含多次可编程存储器（MTP）的副本，该内存在启动时或在专用命令之后加载。影子寄存器包含 IC  的所有配置位。如果出现配置完整性问题，可以通过将“Initialise (01h)”寄存器中的“ReloadMTP”位置为 1 来重新加载。

Bank 110 (6Eh)  ：MTP  用户内存
部分 MTP，可供用户使用。相关详细信息，请参阅章节 6.10.2。


6.10.2 MTP（多次可编程存储器）

6.10.2.1 概述
MTP  是一种多次可编程（非易失性）存储器，用于存储一些用户数据。它还可以存储 IC  的地址（ID），从而显著缩短 POR  或睡眠后的系统启动时间。 MTP由 12 个 16 位宽的寄存器组成：8 个用于存储用户数据的通用寄存器和 4 个功能寄存器。 关于 MTP  寄存器，可以在 表 65  – MTP  寄存器图表中找到相关信息。



DNB110xB 数据手册	23 / 54















需要注意的是，对 MTP  的写入操作次数限制为
255 次。达到此数量后，写入将被阻止。在对 MTP  进行  200   次 编 程 后 ， 一 个 警 告 服 务 请 求 标 志(MTP_wrt_cnt)  将被置位。

6.11 电压测量

6.11.1 概述

(VM)




6.10.2.2 读MTP 影子寄存器
为 了 从 MTP   中 读 取 ， 用 户 首 先 必 须 使 用SetRegBank (0x0F)  命令将寄存器块更改在 123 到 127（7Bh 到 7Fh）的范围内，然后发送一个带有“命令类型[3:0]”字段的命令，指向目标寄存器。 “命令数据[15:0]”字段在读取时并不重要，将被忽略。
需要注意的是，为了访问 IC  的主要功能，用户必须将寄存器组更改回“Bank0”’。
芯片集成了两个完全独立的电压测量 (VM) ADC：主 (VMm)  和辅(VMg)。如果这两个 ADC  的测量结果相差超过 15 mV，服务请求标志 (VM-ADCERr)  将被置位。
输入电压以 4 MHz 的固定频率进行采样。然后通过抽取滤波器降低采样率。用户可以通过设置“获取数据（GetData）”寄存器中的标志位，在 4ms 到 1s 的积分时间中（共 9 档）选择所需要的积分时间。

表 21- VM 采样率设置


6.10.2.3 写MTP
为了写 MTP，用户首先必须使用 SetRegBank (0x0F)命令将寄存器块更改为 110（0x6E），然后发送一个命令，其中“命令数据 [15:0]”字段填充用户数据和'命令 类 型 [3:0]'   字 段 指 向 目 标 寄 存 器 。 这 会 将“Pgm_ongoing”位设置为高电平，一旦编程完成，它将回到零。编程通常需要大约 50  毫秒。
需要注意的是，为了访问 IC  的主要功能，用户必须将寄存器组更改回“Bank0”。









6.10.3 将 ID 和设置写入 MTP

只有将这些寄存器中的“写 MTP”位设置为高，才能将“枚举 Enumerate (00h)”和“初始化 Initialise
(01h)”寄存器中的 IC  地址 (ID)  和其他设置写入 MTP。使用 6.10.2.3  中描述的方法写入这两个寄存器的内容
将无效。
注释 [1]：参见 DNB11xx 软件开发手册，应用使用案例。


6.10.3.1 锁定MTP
MTP  中的用户数据可以通过设置锁定密钥来防止未经授权的写入。
ADC (VMd)  的转换结果是 14  位宽。可以使用GetData(MainVolt)  或 GetData(GuardVolt)  读取数据。然后可以使用公式 1  计算电压。

=  [ 4.8 · (214 − 1) ] + 1.2  [V]

公式1 - VM 结果转换


6.11.2 等间隔/非等间隔

等间隔/非等间隔电压采样使用户可以灵活地决定采样如何随时间分布。















DNB110xB 数据手册	24 / 54















































图 21 - VM 输入滤波器特性

图 20- 等间隔和非等间隔采样

等间隔采样意味着 ADC 在完成一次测量后会立即开始下一次测量。非等间隔采样意味着读取数据将重置ADC  抽取滤波器并立即开始新的采样周期。两种模式如图 20 所示。

6.11.3 VM  输入滤波器特性

DNB110xB  不需要任何额外的外部 RC  元器件即可实现滤波。可以通过更改 VM  的采样率/积分时间来选择不同的带宽设置（表 21）。图 21  是 VM 采样率 [3:0] = 1  和 VM 采样率 [3:0] = 4  的输入滤波器特性示例。
表 22  是第一个陷波的频率与采样率的关系。



6.12 温度测量(TM)

DNB110xB  集成了温度测量传感器。因此，不需要外部温度传感器（如 NTC）。
芯片集成了两个完全独立的芯片温度传感器(DTS)：主温度传感器 (DTSm)  和辅温度传感器 (DTSg)。并持续比较两者的温度读数，如果差异高于默认值，则将置位服务请求标志 (Cell-TempErr)。
为了确保与电芯的最佳热耦合，IC  采用 HTSSOP封装，带有裸露的芯片底部焊盘。两种 DTS  都有 12位宽的输出代码和相同的温度范围，但是 DTSm  仅使用 10  个 MSB（2  个 LSB  始终固定为零）。

表 23- DTS 概览

表 22—不同采样率设置的第一陷波频率

VM   采 样 率
[3:0]	积分时间 [ms]	第一个陷波频率
[Hz]
0	4.1	244
1	8.2	122
2	16.4	61
3	32.8	30.5
4	65.5	15.2
5	131	7.6
6	262	3.8
7	524	1.9
8	1048	0.95






温度测量以二进制补码表示（以下示例）。
0x000 = 0°C 0x800 = -128°C 0x7FF = +127°C
0x001 = 0.0625°C (仅 DTSg) 0x004 = 0.25°C (DTSg  和 DTSm)


6.12.1 自热补偿(SHC)



为了提高温度测量的准确性，DNB110xB  集成了对 IC  自热进行补偿的功能。补偿原理如图 22 所示。







DNB110xB 数据手册	25 / 54

















6.13.1 内部电流源

内部电流源是电流数模转换器(CDAC)，可实现最大 205 mA 电流，并可通过一个 4 位分辨率的寄存器进行均衡电流的设置。

表 24 -内部电流源设置（设置均衡电流SetBalCurr 寄存器）





图 22 – 自热补偿模型



由于存在热阻（公式 2），芯片中消耗的功率 P  会导致芯片温度升高 ΔT。

ΔT = P * Rth [°C],

公式 2 –自热导致的温度升高

注： Rth = Rth_IC + Rth_PCB

由于经过调整的电流功耗和已知的电源电压值，IC  的消耗功率是已知的。在 MTP  中编程 Rth  值后，用户可以读取电池温度（而不是芯片温度）作为已经补偿的温度。公式 3  给出了获得电池温度的公式。. TCELL = TDTS - ΔT [°C]
公式 3 –SHC 公式

需要注意的是，使用内部电流源会使得 IC 中消耗的功率增加，并产生自热，从而增加 DTS 的测量值。

6.13.2 外部电流源

DNB110xB 集成了一个外部 MOSFET 驱动器，可启用外部电流源功能。图 23 是电路的简化示意图。电流源由外部开关和电阻组成。VSW 引脚驱动 MOSFET  的栅极，而 VDR 引脚监控其漏极上的电压。 如果在开启期间电压高于 0.5V 或在关闭期间低于 VBAT-0.5 V，则将 置 位 服 务 请 求 标 志   (VDRcomp)   。


VDR-comps
Vbat-0.5

0.5

Rext


6.13 均衡

ZM-comp


Vbat/2
VDR

VSW



DNB110xB 提供两种电池均衡的方法。在成本敏感型应用中，必须将元器件数量保持在最低限度，用户可

Digital
core
Driver

以使用集成电流源（内部电流源）。如果应用中还需要阻抗测量(ZM)功能，则必须切换到外部电流源，可以将该外部电路用于均衡。通过更改设置阻抗测量电流SetZMCurr (0x06)寄存器中的 enXCS 位的设置来选择外部电流源还是内部电流源。
无论选择何种电流源，DNB110xB 都提供两种不同的均衡方式：基于电压或基于时间。在基于电压的方式中，当电池电压达到设定的电压后或在定义的时间段（超时）后，均衡停止。在基于时间的方式中，将在定义的时间段（超时）后均衡也会停止。

图 23 –MOSFET 驱动器简化原理图

当使用外部电流源进行均衡时，可以通过改变VSW 信号的占空比 (PWM)  来改变均衡电流大小。占空比可以以 12.5%的分辨率分 8 档进行配置（表
25），并且可以使用与阻抗测量相同的一组寄存器来设置频率（表 37）。















DNB110xB 数据手册	26 / 54















表26 – 阻抗测量频率设置

表25 -外部电流源 PWM 设置（设置均衡电流SetBalCurr 寄
存器）


电流/PWM[3:0]	PWM [%]
0x0	12.5
0x2	25
0xE	100
IC  通过在电芯正负极施加特定频率的电流激励，将在电芯两端产生对应频率的交流电压。IC  通过一个连续时间型ADC 来对VZM 进行采样而不需要额外的低通滤波电路。ADC  的输出由IC 中的数字滤波器处理。


有关外部电流源元件选择的更多信息，请参见第
9.1 章。



6.14 阻抗测量 (ZM)

阻抗测量不适用于 DNB1100B。

6.14.1 概述

阻抗测量 (ZM)  是 DNB110xB 的一项关键功能，它可以绘制电池的整个奈奎斯特曲线（图 24 ）。














图 24–奈奎斯特曲线示例
一个采样值生成所需的时间取决于对频率设置时对应
的指数值。阻抗测试所需时间的计算公式：
= 1.05 × 2(7−  ) [ ]

公式5 – 阻抗测量取样时间

因此，生成新样本的最短时间是 1.05  秒（对于
0.95Hz 或更高的频率）。
电容器 CHP 用于绕过内部电阻分压器的某些部分，并允许不同的增益设置。如果使用 G=1 以外的增益，则 CHP 电容器与电阻网络的阻抗一起形成高通滤波器。这限制了阻抗测量中的最低频率范围。表 27 是 CHP= 22 uF 的阻抗测量的典型频率范围。














图 25–阻抗测量应用的简化示意图



图 25 是阻抗测量应用原理图的概览。基于开关(Q)
和电阻器(R)的外部电流源用于施加一个电流通过电芯，
IC  测量在某个频率下的阻抗。要测量完整的奈奎斯特曲线，必须在多个不同频率下测量阻抗。 测量频率设置由公式 4  给出。
表 27 -频率范围与增益(CHP = 22 uF)

=   ×	× 2
公式 4– 阻抗测量频率

其中数值和指数是通过频率寄存器（SetZMFrq）来设置的（见表 26）
k = 7.4506mHZ。













DNB110xB 数据手册	27 / 54

















6.14.2 测量

阻抗测量的结果存储在两个单独的寄存器中。一个（Z(real)）存储阻抗数据的实部，另一个（Z(imag)）存储虚部。每个结果为 16 位宽，由 4 位指数（E(RE)  和E(IM)分别表示实部和虚部）和 12  位尾数（二进制补


不同的交流增益设置将导致输入不同的灵敏度。这将影响输入电压范围，超出该范围 ADC 将开始削波。可以使用以下公式计算最大允许峰峰值电压，以较低的值为准。

码表示，M(RE)和 M(IM)分别表示实部和虚部）组成。可以使用获取实部命令 GetData(Zreal)和获取虚部命令

=

2∙|	−1.68 |

或

=
2∙|5.52 −	|
,

GetData(Zimag)读取。

然后通过如下公式换算出实际值。
=	∙ 2
公式 6 -	阻抗测量结果的表示（实部）

=	∙ 2
公式 7 -	阻抗测量结果的表示（虚部）

表 28– 实部Zreal 和虚部Zimag 数字表示



要测量阻抗，必须知道驱动电流。为了获得此信息，电池的平均电压 (VZM)  测量需与 ZM  测量同步进行。VZM  测量的数字结果 (VZMd)  为 14  位宽，可使用
获取数据命令 GetData(VZM)读取。下面的公式是测量的数字结果 (VZMd)  和电池电压之间的关系。

=  [ 4.8 · (214 − 1) ] + 1.2  [V]

公式 8 – VZM 结果转换

知道电池的电压 (VZM)、Zreal   和 Zimag 后，可以使用以下公式计算阻抗。
公式 11 – 最大限度的ADC 输入峰峰值电压与电池电压


















图 26-不同增益设置的最大允许输入峰峰值电压与电池电压
的关系


6.14.3 低频噪声抑制（LFNS）

将低频噪声抑制 LFNS 位（SetZMFreq [07h]）设置为高，将激活低频噪声抑制。LFNS 仅适用于将尾数的LSB 设置为 0 的频率。如果需要将尾数的 LSB 设置为 1的频率，则必须将尾数乘以 2，并将指数降低 1。请注意，如果指数降低 1  并且指数为 7  或更低（参见第


=
∙

VZM ∙	∙ sinc2(62.5e3)
6.14.1  节），则生成测量样本所需的时间会加倍。
启用 LFNS 低频部分将限制在 15 mHz，因为只有
尾数的 LSB 设置为 1 才能设置为 7.5mHz。

公式9 -阻抗实部转换




=
∙

VZM ∙	∙ sinc2(62.5e3)



公式 10 – 阻抗虚部转换

注:
•	Rext 是电流源中使用的外部电阻器的值
•	k = 1.41·108
•	fexc 是激励频率
•	sinc(x) = sin(πx)/ πx



DNB110xB 数据手册	28 / 54
















6.15 芯片启动

DNB110xB  在电池管理系统 (BMS)  组装期间物理连接至电池上。初始上电后，它进入睡眠模式。然
后，应按照以下顺序设置芯片：

枚举:  为菊花链中的每个 IC 分配一个唯一 ID，从 ID=1开始，并在移动到下一个 IC 时将其加一。此命令不是广播命令，必须在 1 秒内分别向每个 IC 发送两次才能执行。

初始化:  通过广播命令发送的设置命令。这些命令必须在 1 秒内发送两次才会被执行。
IC  提供了将枚举和初始化数据写入 MTP  的可能性。这可以显著加快系统启动速度。




6.16 诊断

6.16.1 服务请求

6.16.1.1 概述

出于诊断原因，每个 IC 都在监控许多内部功能。每当检测到其中一个功能有问题时，就会引发服务请求标志。表 29 显示所有服务请求的概览。 SrvReqData寄存器包含单独的 SR  标志（灰色），以及在引发基础标志之一（颜色编码）时引发的 SR  组。例如，如果“VoltDiagnostics”寄存器中的 OV/UV  位之一升高，则SrvReqData  寄存器中的“Cell-VoltErr”位会升高。

服务请求标志以可靠的方式汇报电池包控制器。每当出现 SR  标志时，IC  将忽略收到的下一条命令，而是回复服务请求确认帧。这个确认帧有一个 ACK 半字节‘0xx1’，作为它的有效载荷，有一个 SrvReqData寄存器的内容。电池包控制器可以立即知道收到了哪些服务请求标志，而无需发送额外的命令。只有引发 SR
标志后的第一个命令才会被否决。接下来的所有
命令都将正常处理。

电池包控制器确认接收到 SR 标志至关重要。如果电池包控制器不知何故错过了 SR 确认，它将不知道潜在的危险情况。检索 SR  标志并不会清除 IC  内的 SR标志。只有在电池包控制器发出“SetSrvReqMask”命令（作为一个握手信号用以通知 IC 电池包控制器已经接收到了 SR  标志）后，所有活动 SR  标志才会被清除。



6.16.1.2 优先级命令
在特定情况下，新的 SR 标志被引发后的服务请求确认帧，尤其是忽略接收到的命令，是不可取的。 例如：如果在 IC 枚举过程中出现 SR 标志，则枚举过程会相当复杂。因此，有些命令是优先命令，这意味着无论是否设置了 SR  标志，它们都将始终以正常确认帧的方式执行和确认。服务请求确认帧在第一个非优先级命令之后。确认帧中的 ACK[2]  位（Ack 半字节）指示产生的“未决”状态的 SR  标志。

以下命令是优先级命令：枚举 Enumerate、初始化Initialize、设置服务请求掩码位 SetSrvReqMask、获取状态 GetStatus、设置寄存器块 SetRegBank  和设置模式SetMode（只有睡眠/待机）。



6.16.1.3 安全相关服务请求
一些服务请求标志被标记为“安全相关标志”。这意味着，如果这些服务请求标志出现，系统会自动转换到安全模式，这也意味着电池的激励（通过阻抗测量或均衡）被停止。



表 29 -服务请求概述















DNB110xB 数据手册	29 / 54















以下 SR  与安全相关，会将 IC  设置成安全模式：
vmMGdiff,	UVolt_G,	OVolt_G,	UVolt_M,	OVolt_M,
TempMG_diff,   MinDieTemp,   MaxDieTemp,   UTemp_G, OTemp_G,   VDRErr,   DAC-Err,   CurrErr,   CRC_shd_DNS, CRC_cmd, MTP_ECC.




6.16.2 MTP 影子寄存器的 CRC 校验

该函数使用多项式 0xAC9A  计算 MTP  影子寄存器的 16  位 CRC。它可用于检查内容的有效性。电池包控制器可以在寄存器的本地副本上计算循环冗余校验CRC，并将其与 DNB110xB 中存储的值进行比较，以检查内容中的任何意外变化。




6.16.3 “设置”寄存器的硬件校验值 (CRC)

该函数使用多项式 0xAC9A 计算设置寄存器的 16位 CRC。它可用于检查内容的有效性。电池包控制器可以在设置寄存器的本地副本上计算循环冗余校验 CRC，并将其与 DNB110xB  中存储的值进行比较，以检查内容中的任何意外变化。

































































DNB110xB 数据手册	30 / 54















7.  指令列表

7.1   概述

表 30  是“Bank0”中可用命令的概述。 “设置”指令类型为“写入”指令，“获取”指令类型为“读取”指令。

表 30 -命令摘要 (bank0)


寄存器	命令	写入/获取	优先命令
00h	枚举（Enumerate）	写入	是
01h	初始化（Initialise）	写入	是
02h	设置 MTP 锁存码（SetMTPLockKey）	写入	否
03h	设置电压阈值（SetThVolt）	写入	否
04h	设置温度阈值（SetThTemp）	写入	否
06h	设置阻抗测量电流（SetZMCurr）	写入	否
07h	设置阻抗测量频率（SetZMFreq）	写入	否
08h	设置均衡电流（SetBalCurr）	写入	否
09h	设置均衡电压（SetBalVolt）	写入	否
0Ah	设置服务请求掩码位（SetSrvReqMask）	写入	是
0Bh	设置模式（SetMode）	写入	否
0Ch	设置命令错误（SetForcedErr）	写入	否
0Dh	获取状态（GetStatus）	获取	是
0Eh	获取数据（GetData）	获取	否
0Fh	设置寄存器库（SetRegBank）	写入	是



7.2   指令详情

7.2.1   枚举 Enumerate (00h)

表 31-枚举（命令 00h）位描述


枚举 (00h)
比特位	名称	描述
15	WriteMTP[1]	此位设置为高会将“枚举（00h）”寄存器的内容写入 MTP
14-10	Reserved[5]	保留
9	SplitBus[1]	0: 启用发送器输出（启用命令传输等）
1: 禁用发送器输出（与 Set ID=0 效果相同）
8	IgnoreBcast[1]	0: 收听广播
1: 忽略广播（指示设备忽略广播命令（仅在 ID 不等于广播地址时起作用））
7-0	SetID[8]	ID，定义 IC 身份（地址）
POR 状态对应的值：0 或 MTP 中存储的值
POR: 当 ID 保存在 MTP 里面，0000h 或 00xxh

















DNB110xB 数据手册	31 / 54

















7.2.2   初始化 Initialisation	(01h)

表 32 -初始化（命令 01h）位描述


初始化 (01h)
比特位	名称	描述
15	WriteMTP[1]	0: 无操作
1: 将 AutoStb[2] 和 NrOfICsInChain[8] 存储到 MTP
14	Reserved[1]	保留[1]
13	ReloadMTP[1]	0: 无操作
1: 重新加载 MTP(1)
12	EnSrvReq[1]	0: 禁用服务请求
1: 允许服务请求
11	GenPOR[1]	0: 无操作
1: 产生上电复位
10	ResetID[1]	0: 无操作
1: 重置 ID（不在 MTP 中）
9-8	AutoStb[2]	0x: 没有自动待机
10: 自动待机，在 DIO 总线空闲 5 分钟后激活，如果均衡处于活动状
态，IC 将等到完成后再进入待机模式
11: 自动待机。在 DIO 总线空闲 5 分钟后激活，IC 将关闭正在执行的均衡或阻抗测量并进入待机模式
7-0	NrOfICsInChain[8]	链中的 IC 数量(2)
POR: 0300h

注:

(1)   –如果 MTP ECC  服务请求标志被置位，将重新加载 MTP。
(2)   –出于同步测量的目的，每个 IC  需要知道在执行命令之前等待多长时间，为了计算它需要知道链中 IC 的
总数。

7.2.3   设置 MTP 锁存码 SetMTPLockKey（02h）

表 33– 设置MTP 锁存码（命令 02h）位说明


设置 MTP 锁存码 (02h)
比特位	名称	描述
15-0	SetMTPLockKey
[16]	设置 MTP 锁存码
POR: 0000h



7.2.4   设置电压阈值 SetThVolt	(03h)

表 34—设置电压阈值(命令 03h) 位说明


设置电压阈值 (03h)
比特位	名称	描述
15-8	VoltThOver[8]	0-255: 映射到 1.2 – 6.0 V (VM)；（18.8 mV /LSB）
7-0	VoltThUnder[8]	0-255: 映射到 1.2 – 6.0 V (VM)；（18.8 mV /LSB）
POR: FF00h



通过发送“SetThVolt”命令可以设置过压和欠压阈值。这些值是线性的。过压阈值的步长为 18.8 mV。电压编码：
00000000 = 1.2 V
00000001 = 1.2188 V
POR  值有效地禁用了阈值。

DNB110xB 数据手册	32 / 54

















7.2.5   设置温度阈值 SetThTemp	(04h)

表 33 – 设置温度阈值 (命令 04h)位说明


设置温度阈值 (04h)
比特位	名称	描述
15-8	TempThOver[8]	0-255: 映射到 -128 … +127 摄氏度(1k/LSB)
7-0	TempThUnder[8]	0-255: 映射到 -128 … +127 摄氏度(1k/LSB)
POR: FF00h



通过发送“SetThTemp”命令可以设置过温和欠温阈值。温度编码是二进制补码：
10000000 = -128 摄氏度10000001 = -127 摄氏度00000000 = 0 摄氏度01111111 = +127 摄氏度

POR  值有效地禁用了阈值。



7.2.6   设置阻抗测量电流 SetZMCurr	(06h)

表 36– 设置阻抗测量电流 (命令 06h) 位说明


设置阻抗测量电流 (06h)
比特位	名称	描述
15-12	Reserved[4]	保留
11	EnaZM[1]	0: 停止阻抗测量
1: 开始阻抗测量
10	enXCS[2]	0: 内部电流源（仅支持均衡）
1: 外部电流源（带外部 MOSFET）
9-8	HiPass[3]	00: Gain = 1 01: Gain = 4 10: Gain = 16 11: Gain = 16
7-0	ZMTimeOut [4]	1-255: 1 LSB = 134 秒
0: 无阻抗测量
POR: 0000h



[1]EnaZM	如果命令是单独的命令，或者以同步方式，又或者是广播命令时，立即开始/停止阻抗测量。

[2]enXCS	为高电平时，激活外部电流源以进行外部均衡和阻抗测量。

[3]HiPass	设置 ZM-ADC  的增益。
[4]ZMTimeOut  设置阻抗测量 (ZM)  的最长时间。 最大超时值约为 9.5  小时。 当编程时间结束时，电流源将被关闭，服务请求标志（超时）将被置位。
如果任何安全相关的服务请求标志被置位，那么阻抗测量将立即停止。

















DNB110xB 数据手册	33 / 54

















7.2.7   设置阻抗测量频率 SetZMFreq	(07h)

表 37 – 设置阻抗测量频率 (命令 07h) 位说明


设置阻抗测量频率 (07h)
比特位	名称	描述
15-13	Reserved[3]	保留
12	LFNS[1]	低频噪声抑制
0: 关
1: 开
11-8	FRQexponent[4]	0-15: 将一对一映射到阻抗测量频率的指数项
7-0	FRQMantissa[8]	0-255: 将一对一映射到阻抗测量频率的尾数项
POR: 0000h



可以使用公式 12  设置阻抗测量频率。

=   ×	× 2
公式 2 – 阻抗测量频率

最大可编程频率为 7.8125KHz (1/128 µs)。FRQexponent[4]：频率的指数（E）FRQmantissa[8]:  频率尾数 (M)
k = 7.4506 mHz

对于等于或高于 0.9537 Hz  的频率，尾数应在 M = 1  到 255  的范围内，指数 E = 7  到 15。对于低于 0.9537
Hz  的尾数应为 M = 1，指数 E = 0  到 6。

7.2.8   设置均衡电流 SetBalCurr	(08h)

表 38 – 设置均衡电流 (命令 08h) 位说明


设置均衡电流 (08h)
比特位	名称	描述
15-13	Reserved[3]	保留
12	EnBal[1]	0: 停止均衡
1: 开始均衡
11-8	Current/PWM[4]	[11:8]: 1 LSB = 13.3 mA(1)（最大电流为 205 mA）
或
[11:9]: 1 LSB = 12.5%(2) ,3MSB 有效，8 个步进。
7-0	BalTimeOut [8]	0-255: 1 LSB = 134 秒 (最长时间约 9.5 小时)
POR: 0000h



(1)   –仅适用于内部电流源
(2)   –仅适用于外部电流源

BalTimeOut  设置均衡的最长超时时间约为 9.5 小时。当编程时间结束时，电流源将被关闭，服务请求标志（超时）将被置位。
如果任何安全相关的服务请求标志被置位，那么均衡将立即停止。



DNB110xB 数据手册	34 / 54

















7.2.9   设置均衡电压 SetBalVolt	(09h)

表 39– 设置均衡电压 (命令 09h) 位说明


设置均衡电压 (09h)
比特位	名称	描述
15	Reserved[1]	保留
14	BalMode[1]	0: 基于时间的均衡
1: 基于电压的均衡
13-0	BalVolt[14]	0-16383: 映射到 1.2 – 6.0 V
此处使用完整的 14 位 ADC 分辨率
POR: 03FFh



7.2.10 设置服务请求掩码位 SetSRMask (0Ah)

表 40– 设置服务请求掩码位 (命令 0Ah) 位说明


设置中断使能位 (0Ah)
比特位	名称	描述
15-0	ServiceReqMask[16]	bit 15: 保留
bit 14: 不可屏蔽
bit 13: 时钟错误掩码位
bit 12: 寄存器完整性错误掩码位bit 11: 保留
bit 10: 不可屏蔽
bit 9: 保留
bit 8: 阻抗测量 ADC 错误掩码位bit 7: 均衡/阻抗测量完成掩码位bit 6: 电流错误掩码位
bit 5: 内部 LDO 电压超过窗范围掩码位bit 4: 温度 ADC 错误掩码位
bit 3: 电芯温度错误掩码位
bit 2: 电压测量 ADC 错误掩码位bit 1: 电芯电压错误掩码位
bit 0: 欠电断电掩码位
POR: 0000h



如果任何与安全相关的服务请求标志被置位，IC  将进入安全模式。 如需更多信息，请参阅第 6.5  章节。

7.2.11 设置模式 SetMode (0Bh)

表 41– 设置模式 (命令 0Bh) 位说明


设置模式 (0Bh)
比特位	名称	描述
15-4	Reserved[12]	保留
3-0	OperatingMode[4]	0000: 保留0001: 睡眠0010: 保留0011: 待机0100: 正常0101: 保留0110: 自检0111: 保留
1000: 保留 (不可设置 安全 模式)
POR: 0000h





DNB110xB 数据手册	35 / 54















表 42-不同操作模式下的 DNB110xB 功能


操作模式	睡眠	待机	正常	安全	自检	SPI-正常
ID 和寄存器设置
是否保持？	
(1)
否	
是	
是	
是	
是	
是
差分 I/O	关闭(总线活动检测
开启)	
关闭(总线活动检测开启)	

开启	

开启	

开启	

只有 DIOTOP
电压测量	
否	
否	
是
(无需命令触发)	
是
(无需命令触发)	
是	
否
芯片温度感应	
否	
否	
是
(无需命令触发)	
是
(无需命令触发)	
是	
是
阻抗测量	
否	
否	是
(由命令触发)	
否	
否	
否
均衡	
否	
否	是
(由命令触发)	
否	
否	
否
服务请求标志	
否	
否	
是	
是	
是	
(2)
受限制

(1) –除非存储在 MTP 中
(2) –SPI 正常模式下不可用的服务请求标志：ZM-ADCErr、Bal/ZMDone、CurrErr、VM-ADCErr、Cell-VoltErr 以及和这些相关的标志

7.2.12 设置强制报错 SetForcedError (0Ch)

表 43—设置强制错误 (命令 0Ch) 位说明



设置命令错误 (0Ch)
比特位	名称	描述
15-13	Reserved[3]	保留
12	VDRComp[1]	强制 VDR 比较器错误
11	Reserved[1]	保留
10	UTOT[1]	强制 UT/OT 比较器错误
9	DTSComp[1]	强制 DTS 比较器错误
8	UVOV[1]	强制 UV/OV 比较器错误
7	VMComp[1]	强制 VM 比较器错误
6	LDO_FSM[1]	强制 FSM LDO 比较器错误
5	Reserved[1]	保留
4	G_LDO_Ana[1]	强制 辅模拟 LDO 比较器错误
3	G_LDO_Dig[1]	强制 辅数字 LDO 比较器错误
2	G_LDO_PLL[1]	强制 辅 PLL LDO 比较器错误
1	M_LDO_Ana[1]	强制 主模拟 LDO 比较器错误
0	M_LDO_Bal[1]	强制 主均衡 LDO 比较器错误
POR: 0000h



将其中一位设置为高将生成相关的服务请求标志。 如果服务请求标志没有置位，则意味着 IC  可能有故障并且可能需要更换。





DNB110xB 数据手册	36 / 54

















7.2.13 获取状态 GetStatus(0Dh)

表44–获取状态(命令0Dh)位说明



获取状态 (0Dh)
比特位	名称	描述
15-5	Reserved[11]	
4-0	StatusType[5]	请求状态类型:
02h: 一般状态
09h: 锁相环状态
10h: 均衡状态
12h: 服务请求数据
13h: 电压诊断
14h: 温度诊断
15h: 电流诊断
16h: 阻抗测量诊断
18h: IC 诊断其它: 保留
POR: 0000h



一般状态

表 45– 一般状态详细信息



GetStatus (0Dh)	
Status type	
15	
14	
13	
12	
11	
10	
9	
8	
7	
6	
5	
4	
3	
2	
1	
0

GeneralStatus	
0x02	
Reserved	
pgm_ongoing	
Reserved	
MTPBootDone	
Previous_FSMStatus	
FSMStatus



 pgm_ongoing
0: MTP 编程未进行/未完成 1: MTP 编程正在进行中
 MTPBootDone
0: MTP 启动过程在进行 1: MTP 启动过程已完成
 Previous_FSMStatus表示之前的 FSM 状态
 FSMStatus
指示当前 FSM 状态

锁相环状态

表46– 锁相环状态详细信息



GetStatus (0Dh)	
Status type	
15	
14	
13	
12	
11	
10	
9	
8	
7	
6	
5	
4	
3	
2	
1	
0

PLLStatus	0x09	
Reserved	
PLLLocked	
Reserved	
PLL-Coarse	PLL-Fine



 PLLLocked
0: 锁相环未锁定 1: 锁相环锁定
 PLL-Coarse
当前锁相环时钟粗调
 PLL-Fine
当前锁相环时钟细调

均衡状态

表 47 – 均衡状态详细信息



GetStatus (0Dh)	
Status type	
15	
14	
13	
12	
11	
10	
9	
8	
7	
6	
5	
4	
3	
2	
1	
0

BALStatus	
0x10	
Reserved	
BalancingCounter



 BalancingCounter:
基于时间均衡的计数。可用于检查 IC 到目前为止均衡了多长时间。
 1LSB = 134 秒



DNB110xB 数据手册	37 / 54















服务请求数据

表 48 –服务请求数据详细信息




GetStatus (0Dh)	
Status type	
15	
14	
13	
12	
11	
10	
9	
8	
7	
6	
5	
4	
3	
2	
1	
0

SrvReqData	
0x12	
Reserved	
CmdErr	
ClockErr	
IntErr	
Reserved	
InvalidLockKey	
Reserved	
ZM-

ADCErr	
Bal/ZMDone	
CurrErr	
LDOOoR	
Temp-

ADCErr	
Cell-

TempErr	
VM-

ADCErr	
Cell-

VoltErr	
UVLO



 CmdErr
1 秒内没有发送两次有效命令
 ClockErr
锁相环削波
 IntErr
内存完整性错误。 相关 SR 标志：CRC_shdw_DNS, CRC_shdw_user, CRC_cmd, MTP_wrt_cnt, MTP_ECC, MTP_invalid
 InvalidLockKey
访问 MTP 时的锁定密钥无效。
 ZM-ADCErr
ZM ADC 错误。相关 SR 标志：ZMadc_clip, VSWph_clip
 Bal/ZMDone
均衡或 ZM 超时。相关 SR 标志：BalVoltLim,BalTmOut,ZMTmOut
 CurrErr
内部/外部电流源错误。相关 SR 标志：VDRErr, DAC-Err
 LDOOoR
LDO 电压超出范围。相关 SR 标志：LDO-FSM, G_LDO_DIO, G_LDO_Ana, G_LDO_Dig, G_LDO_PLL, M_LDO_Ana, M_LDO_Bal
 Temp-ADCErr
TM 错误。相关 SR 标志：TempMG diff, MinDieTemp, MaxDieTemp
 Cell-TempErr
TM 过温欠温。相关 SR 标志：UTemp_G, OTemp_G
 VM-ADCErr
电压采样 ADC 错误。相关 SR 标志：vmMGdiff, VMadc_Gclip, VMadc_Mclip
 Cell-VoltErr
电压采样过压欠压错误。相关 SR 标志：UVolt_G, OVolt_G, UVolt_M, OVolt_M
 UVLO
欠电断电错误。相关 SR 标志：Blackout, Brownout

电压诊断

表 49– 电压诊断详细信息



GetStatus (0Dh)	
Status type	
15	
14	
13	
12	
11	
10	
9	
8	
7	
6	
5	
4	
3	
2	
1	
0

VoltDiagnostics	
0x13	
Reserved	
Blackout	
Brownout	
adcMGdiff	
VMadc_Gclip	
VMadc_Mclip	
UVolt_G	
OVolt_G	
UVolt_M	
OVolt_M



 Blackout
检测到断电/POR（VBAT 低于 1.5 V）； 初始 POR 后始终为高
 Brownout
检测到欠电（VBAT 低于 1.9 V）
 adcMGdiff
主电压采样ADC 和辅电压采样 ADC 之间的电压差高于 15 mV
 VMadc_Gclip
辅电压采样 ADC 削波
 VMadc_Mclip
主电压采样ADC 削波
 UVolt_G
辅电压采样低于欠压阈值
 OVolt_G
辅电压采样高于过压阈值
 UVolt_M
主电压采样低于欠压阈值



DNB110xB 数据手册	38 / 54















OVolt_M
主电压采样高于过压阈值
温度诊断

表 50 – 温度诊断详细信息



GetStatus (0Dh)	
Status type	
15	
14	
13	
12	
11	
10	
9	
8	
7	
6	
5	
4	
3	
2	
1	
0

TempDiagnostics	
0x14	
Reserved	
TempMG diff	
MinDieTemp	
MaxDieTemp	
UTemp_G	
OTemp_G	
Reserved



 TempMG diff
主 DTS 与辅 DTS 温差大于默认值
 MinDieTemp
芯片温度低于 -40°C
 MaxDieTemp
芯片温度高于 +105°C
 UTemp_G
辅 DTS 低于欠温阈值
 OTemp_G
辅 DTS高于过温阈值
电流诊断

表 51– 电流诊断详细信息



GetStatus (0Dh)	
Status type	
15	
14	
13	
12	
11	
10	
9	
8	
7	
6	
5	
4	
3	
2	
1	
0

CurrDiagnostics	
0x15	
Reserved	
BalVoltLim	
BalTmOut	
ZMTmOut	
VDRErr	
DAC-Err



 BalVoltLim
基于电压的均衡已达到阈值并已停止
 BalTmOut均衡超时
 ZMTmOut
阻抗测量超时
 VDRErr
外部开关 (MOSFET) 问题。 导通期间漏源电压不够低或关断期间漏源电压不够高。
 DAC-Err
数模转换器电流错误

阻抗测量诊断

表 52 – 阻抗测量诊断详细信息



GetStatus (0Dh)	
Status type	
15	
14	
13	
12	
11	
10	
9	
8	
7	
6	
5	
4	
3	
2	
1	
0

ZMDiagnostics	
0x16	
Reserved	
VSWph_clip	
ZMadc_clip



 VSWph_clip VSW 相位削波
 ZMadc_clip
阻抗采样ADC 削波



IC诊断

表 53 – IC 诊断详细信息



GetStatus (0Dh)	
Status type	
15	
14	
13	
12	
11	
10	
9	
8	
7	
6	
5	
4	
3	
2	
1	
0

ICDiagnostics	
0x18	
Reserved	
CRC_shdw_DNS	
CRC_shdw_user	
CRC_cmd	
MTP_wrt_cnt	
MTP_ECC	
MTP_invalid	
LDO_FSM	
Reserved	
G_LDO_Ana	
G_LDO_Dig	
G_LDO_PLL	
M_LDO_Ana	
M_LDO_Bal



 CRC_shdw_DNS



DNB110xB 数据手册	39 / 54















MTP 的 原厂数据部分的 CRC 错误
 CRC_shdw_user
MTP  的用户数据部分的 CRC 错误
 CRC_cmd
在 bank0 的设置寄存器中检测到 CRC 错误
 MTP_wrt_cnt
超过对 MTP 的最大写入操作数
 MTP_ECC
MTP  内存完整性错误
 MTP_invalid
MTP  未编程
 LDO_FSM
FSM LDO  超出范围
 G_LDO_Ana
辅模拟 LDO 超出范围
 G_LDO_Dig
辅数字 LDO 超出范围
 G_LDO_PLL
辅锁相环 LDO 超出范围
 M_LDO_Ana
主模拟 LDO 超出范围
 M_LDO_Bal
主均衡 LDO 超出范围

7.2.14 获取数据 GetData (0Eh)

表 54 – 获取数据(命令 0Eh) 位说明



获取数据 (0Eh)
比特位	名称	描述
15	rst_ZMph [1]	0: 无操作
1: 复位内部产生的阻抗测量信号的相位
14	ClrExeCnt[1]	0: 无操作
1: 重置“两次发送命令”计数器
13	Equidist[1]	0: 非等间隔电压采样
1: 等间隔电压采样
12	ReserRSC[1]	0: 无操作
1: 重置滚动样本计数器
11-8	VMSampleRate[4]	设置电压测量采样率(1)最大设定值 = 8 (08h)
7-6	Reserved[2]	保留
5-0	DataType[6]	00h: 主 ADC 电压 (VMm)
01h: 辅 ADC 电压 (VMg)
02h: 主电池温度 (DTSm_cell)
03h: 辅电池温度 (DTSg_cell)
04h: 主芯片温度 (DTSm)
05h: 辅芯片温度 (DTSg)
06h: VZM ADC 电压
07h: 阻抗实部
08h: 阻抗虚部
09h: 唯一 ID1


DNB110xB 数据手册	40 / 54















0Ah: 唯一 ID2 0Bh: 唯一 ID3 0Ch: 唯一 ID4
0Dh: HwChksumMTP; MTP 影子寄存器的硬件校验值 (CRC)
0Eh: HwChksumSet; “设置”寄存器的硬件校验值 (CRC) 0Fh: 产品版本

POR: 0000h

注:
(1)   通过设置积分周期 t = 2SampleRate x 214 / 4MHz  来调整采样率。 如果采样率设置为 0，则积分周期 t = 20 x
214 / 4e6 = 4.096 ms。 最大值为 8， t = 1.0486  秒
SampleRate  和 Equidist  设置仅适用于电压测量 (VM)。

7.2.15 设置寄存器块 SetRegBank (0Fh)

表 55 – 设置寄存器块 (命令 0Fh) 位说明



设置寄存器库 (0Fh)
比特位	名称	描述
15-9	Reserved[7]	保留
8	RW[1]	0: 写入寄存器
1: 读取寄存器
7	Reserved[1]	保留
6-0	BankAddress[7]	块地址
POR: 0000h





8.  SPI 时序



t1	t2



SCK



t3	t4



MOSI



t5



MISO



图27 - SPI 时序



表 56 - SPI 时序规范


名称	描述	最小	典型值	最大	单位
T1	SCK  高电平时间		500		ns
T2	SCK  低电平时间		500		ns
T3	数据保持时间	250			ns
T4	数据建立时间	50			ns
T5	从 SCK  到 MISO 稳定的延迟			250	ns







DNB110xB 数据手册	41 / 54















9.  应用

9.1   原理图和元器件选择

9.1.1   DNB110xB 在 SPI 模式















































图 28 – SPI 模式下的DNB110xB



表 57 – SPI 模式下的DNB110xB; 元器件选择



编号	元器件	值	制造商	型号	备注
U101	IC		大唐恩智浦	DNB1101B	
R101	上拉电阻	4.7 kΩ	威世达勒	CRCW06034K70JNEB	5%,	125	mW,	AEC-

Q200
R102, R103	电阻	见公式 13			5%,	125	mW,	AEC-

Q200
R104	电阻	150 ohm	威世达勒	TNPW1206150RFEEA	5%,	250	mW,	AEC-

Q200
C101	去耦电容	10 uF	村田	GCM31CR71C106MA64L	X7R, >16 V, AEC-Q200
C102	去耦电容	100 nF	村田	GRM155R71E104KE14D	X7R, >25 V, AEC-Q200
C103	去耦电容	10 nF	村田	GCM3195G1H103JA16D	X8G, 50V, AEC-Q200
T101	变压器		普思电子	HM2108NL,	HM2103NL
或类似的	AEC-Q200
D101,D102	瞬态二极管		安世半导体	PESD5V0V1BL	AEC-Q101

















DNB110xB 数据手册	42 / 54















公式 3 - R102, R103 选择




102,103[Ω] =
− 300 1.875





9.1.2   测量模式下的带阻抗测量的 DNB110xB




















图 29- 测量模式下的带阻抗测量的DNB110xB




表 58 - 测量模式下的带阻抗测量的DNB110xB; 元器件选择



编号	元器件	值	生产商	型号	备注
U1 - U12	IC		大唐恩智浦	DNB1101B	
R1 - R12	功率电阻	见表 59			
R13, R14	电阻	150 ohm	威世达勒	TNPW1206150RFEEA	5%, 250 mW, AEC-Q200
C1, C34	去耦电容	10 uF	村田	GCM31CR71C106MA64L	X7R, >16 V,

AEC-Q200
C2 ,C35	旁路电容	22 uF	村田	GCM32ER71C226ME19L	X7R, >16 V,

size: 1210 (preferred)

AEC-Q200
C3, C36	去耦电容	100 nF	村田	GRM155R71E104KE14D	X7R, >25 V,

AEC-Q200
C37,C38	去耦电容	10 nF	村田	GCM3195G1H103JA16D	X8G, 50V, AEC-Q200
T1, T2	变压器		普思电子	HM2108NL, HM2103NL
或类似的	AEC-Q200
Q1 – Q12	MOS 管		安世半导体	PMV28UNEA	AEC-Q101
D1-D6, D47-
D52	瞬态二极管		安世半导体	PESD5V0V1BL	AEC-Q101







DNB110xB 数据手册	43 / 54















表 59 – 功率电阻; 元件选择



均衡
电流@4V
(Imax)	阻抗测量
激励电流
@4V (Imean)	值	容差	最大功率
@ 4.5V	TCR	等级
100 mA	50 mA	39 Ω	< 1%	0.5 W	< 100 ppm/°C	AEC-Q200
200 mA	100 mA	20 Ω	< 1%	1 W	< 100 ppm/°C	AEC-Q200
300 mA	150 mA	13.3 Ω	< 1%	1.5 W	< 100 ppm/°C	AEC-Q200
400 mA	200 mA	10 Ω	< 1%	2 W	< 100 ppm/°C	AEC-Q200
500 mA	250 mA	8 Ω	< 1%	2.5 W	< 100 ppm/°C	AEC-Q200



典型应用指南：外部功率电阻值约为电池内阻的 20000  倍。

9.1.3   测量模式下的不带阻抗测量的 DNB110xB






















图 30	测量模式下的不带阻抗测量的DNB110xB

表 60 - 测量模式下的不带阻抗测量的DNB110xB; 元器件选择



编号	元器件	值	生产商	型号	备注
U1 - U12	IC		大唐恩智浦	DNB1100B	
R13, R14	电阻	150 ohm	威世达勒	TNPW1206150RFEEA	5%, 250 mW, AEC-

Q200
C1..C34	去耦电容	10 uF	村田	GCM31CR71C106MA64L	X7R, >16 V,

AEC-Q200
C3..C36	去耦电容	100 nF	村田	GRM155R71E104KE14D	X7R, >25 V,

AEC-Q200
C37,C38	去耦电容	10 nF	村田	GCM3195G1H103JA16D	X8G,	50V,	AEC-

Q200
T1, T2	变压器		普思电子	HM2108NL,	HM2103NL
或类似的	AEC-Q200



DNB110xB 数据手册	44 / 54















D1,D2,D51,D52	瞬态二极管	安世半导体	PESD5V0V1BL	AEQ-101



9.1.4   相同电压域的 IC 处理

当 IC  处于相同的电压域（连接到相同的电源）时，需要在通信总线上增加额外的电容器。

表 61– 同一电压域中的IC 链


编号	元器件	值	生产商	型号	备注
Cp, Cn	电容	100 nF	村田	GRM155R71E104KE14D	X7R, >25 V,

AEC-Q200

























图 31 – 同一电压域的IC; 电容器隔离解决方案



















































































DNB110xB 数据手册	45 / 54















10.	寄存器列表



表 62 – 内存块0 寄存器图




COMMAND	
Register	
15	
14	
13	
12	
11	
10	
9	
8	
7	
6	
5	
4	
3	
2	
1	
0

Enumerate	
0x00	
WriteMTP	
Reserved	
SplitBus	
IgnoreBcast	
SetID

Initialise	
0x01	
WriteMTP	
Reserved	
ReloadMTP	
EnSrvReq	
GenPOR	
ResetID	
AutoStb	
NrOfICslnChain

SetMTPLockKey	
0x02	
MTPLockKey

SetThVolt	
0x03	
ThOver_Volt	
ThUnder_Volt

SetThTemp	
0x04	
ThOver_Temp	
ThUnder_Temp

Reserved	
0x05	
Reserved

SetZMCurr	
0x06	
Reserved	
EnZM	
enXCS	
HiPass	
ZMTimeOut

SetZMFreq	
0x07	
Reserved	
LFNS	
FRQExponent	
FRQMantissa

SetBalCurr	
0x08	
Reserved	
EnBal	
Current/PWM	
BalTimeOut

SetBalVolt	
0x09	
Reserved	
BalMode	
BalVolt

SetSrvReqMask	
0x0A	
Reserved	
Non-

maskable	
ClockErr	
IntErr	
Reserved	
Non-

maskable	
Reserved	
ZM-ADCErr	
Bal/ZMDone	
CurrErr	
LDOOoR	
Temp-

ADCErr	
Cell-

TempErr	
VM-ADCErr	
Cell-VoltErr	
UVLO

SetMode	
0x0B	
Reserved	
OperatingMode

SetForcedError	
0x0C	
Reserved	
VDRComp	
Reserved	
UTOT	
DTSComp	
UVOV	
VMComp	
LDO_FSM	
Reserved	
G_LDO_Ana	
G_LDO_Dig	
G_LDO_PLL	
M_LDO_Ana	
M_LDO_Bal

GetStatus	
0x0D	
Reserved	
StatusType

GetData	
0x0E	
rst_ZMph	
ClrExeCnt	
Equidist	
ResetRSC	
VMSampleRate	
Reserved	
DataType

SetRegBank	
0x0F	
Reserved	
RW	
Reserved	
BankAddress











DNB110xB 数据手册	46 / 54















表 63 – 获取状态命令寄存器图




获取状态 (0Dh)	
状体类型	
15	
14	
13	
12	
11	
10	
9	
8	
7	
6	
5	
4	
3	
2	
1	
0

GeneralStatus	
0x02	
Reserved	
pgm_ongoing	
Reserved	
MTPBootDone	
Previous_FSMStatus	
FSMStatus

PLLStatus	
0x09	
Reserved	
PLLLocked	
Reserved	
PLL-Coarse	
PLL-Fine

BALStatus	
0x10	
Reserved	
BalancingCounter

SrvReqData	
0x12	
Reserved	
CmdErr	
ClockErr	
IntErr	
Reserved	
InvalidLockKey	
Reserved	
ZM-ADCErr	
Bal/ZMDone	
CurrErr	
LDOOoR	
Temp-ADCErr	
Cell-TempErr	
VM-ADCErr	
Cell-VoltErr	
UVLO

VoltDiagnostics	
0x13	
Reserved	
Blackout	
Brownout	
vmMGdiff	
VMadc_Gclip	
VMadc_Mclip	
UVolt_G	
OVolt_G	
UVolt_M	
OVolt_M

TempDiagnostics	
0x14	
Reserved	
TempMG diff	
MinDieTemp	
MaxDieTemp	
UTemp_G	
OTemp_G	
Reserved

CurrDiagnostics	
0x15	
Reserved	
BalVoltLim	
BalTmOut	
ZMTmOut	
VDRErr	
DAC-CurrErr

ZMDiagnostics	
0x16	
Reserved	
VSWph_clip	
ZMadc_clip

ICDiagnostics	
0x18	
Reserved	
CRC_shdw_DNS	
CRC_shdw_user	
CRC_cmd	
MTP_wrt_cnt	
MTP_ECC	
MTP_invalid	
LDO_FSM	
Reserved	
G_LDO_Ana	
G_LDO_Dig	
G_LDO_PLL	
M_LDO_Ana	
M_LDO_Bal







































DNB110xB 数据手册	47 / 54















表 64 – 获取数据命令寄存器图




获取数据 (0Eh)	数据类型	
15	
14	
13	
12	
11	
10	
9	
8	
7	
6	
5	
4	
3	
2	
1	
0

MainVolt	0x00	
Reserved	
Main Voltage

GuardVolt	0x01	
Reserved	
Guard Voltage

MainCellTemp	0x02	
Reserved	
Main Cell temperature

GuardCellTemp	0x03	
Reserved	
Guard Cell temperature

MainDieTemp	0x04	
Reserved	
Main Die Temperature

GuardDieTemp	0x05	
Reserved	
Guard Die Temperature

VZM	0x06	
Reserved	
ZM Voltage

Zreal	0x07	
ZExponent	
ZMantissa

Zimag	0x08	
ZExponent	
ZMantissa

UniqueID1	0x09	
p_id_Batch[23:8]

UniqueID2	
0x0A	
p_id_Batch[7:0]	
p_id_wafer[23:16]

UniqueID3	
0x0B	
p_id_wafer[15:0]

UniqueID4	
0x0C	
p_id_y[7:0]	
p_id_x[7:0]

HwChksumMTP	
0x0D	
HwChksumMTP

HwChksumSet	
0x0E	
HwChksumSet

Product version	
0x0F	
Mask version	
Metal version	
MTP version	
IC type





























DNB110xB 数据手册	48 / 54















表 65– MTP 寄存器图




读取地址	
写地址	


POR valule	


15	


14	


13	


12	


11	


10	


9	


8	


7	


6	


5	


4	


3	


2	


1	


0	


描述




库	



地址	



库	



地址																		







0x7E	
0x00	






0x6E	
0x00	
0x0000	
General purpose register 1	
通用 RW 区
	
0x01		
0x01	
0x0000	
General purpose register 2	
	
0x02		
0x02	
0x0000	
General purpose register 3	
	
0x03		
0x03	
0x0000	
General purpose register 4	
	
0x04		
0x04	
0x0000	
General purpose register 5	
	
0x05		
0x05	
0x0000	
General purpose register 6	
	
0x06		
0x06	
0x0000	
General purpose register 7	
	
0x07		
0x07	
0x0000	
General purpose register 8	
	
0x08		
0x08	
0x001E	
Reserved	
Rth	
用于自热计算
	
0x09		
0x09	
0x0000	
MTP lock key	

0x7D	
0x0D	
n.a.	
n.a.	
0x0000	
Reserved	
SplitBus	
Reserved	
SetID	
枚举信息
	
0x0E		
n.a.	
0x0300	
Reserved	
AutoStb	
NrOfICslnChain	
初始化信息





注：只有将这些寄存器中的“WriteMTP”位置为 1，才能写入包含枚举和初始化信息的寄存器。参见章节 6.10.3“将 ID 和设置写入 MTP”。

























DNB110xB 数据手册	49 / 54















11.	封装



HTSSOP20 封装； 20 引脚；0.65mm 脚距，4.4mm 宽度； 裸露的芯片焊盘。































































































































DNB110xB 数据手册	50 / 54

























































































































































DNB110xB 数据手册	51 / 54















12.	参考文献





1:DNB11xx 软件开发手册，此软件开发手册文件可从大唐恩智浦半导体获得。































































































































DNB110xB 数据手册	52 / 54















13.	修订历史


修订	日期	变更说明
1.1	11/30/2021	DNB110xA 的一般修订，删除初步状态
2.0	21/12/2022	DNB110xB 的发行



























































































































DNB110xB 数据手册	53 / 54















14.	法律信息



14.1 免责声明



有限保修和责任 — 本文档中的信息被认为是准确和可靠的。 但是，大唐恩智浦半导体不对此类信息的准确性

或完整性作出任何明示或暗示的陈述或保证，并且不对使用此类信息的后果承担任何责任。

在任何情况下，大唐恩智浦均不对任何间接、附带、惩罚性、特殊或后果性损害（包括但不限于利润损失、储蓄损失、业务中断、与任何产品的拆除或更换相关的成本或返工费用）承担责任 无论此类损害赔偿是否基于侵权（包括疏忽）、保证、违约或任何其他法律理论。

尽管客户可能因任何原因遭受任何损害，大唐恩智浦半导体对此处描述的产品对客户的总和累积责任应根据大唐恩智浦半导体的商业销售条款和条件进行限制。



更改权 — 大唐恩智浦半导体保留随时更改本文档中发布的信息的权利，包括但不限于规格和产品描述，恕不另

行通知。 本文档取代并替换在此发布之前提供的所有信息。



使用适用性 —大唐恩智浦半导体产品未设计、授权或保证适用于生命支持、生命关键或安全关键系统或设备，

也不适用于应用可以合理预期大唐恩智浦半导体产品的故障或故障导致人身伤害、死亡或严重的财产或环境损害。
大唐恩智浦半导体对在此类设备或应用中包含和/或使用大唐恩智浦半导体产品不承担任何责任，因此此类包含和/或使用的风险由客户自行承担。.

应用程序 — 本文描述的任何这些产品的应用程序仅用于说明目的。大唐恩智浦半导体不声明或保证此类应用将

适用于指定用途，无需进一步测试或修改。

客户应对使用大唐恩智浦产品的应用和产品的设计和操作负责，大唐恩智浦对应用或客户产品设计方面的任何协助不承担任何责任。 确定大唐恩智浦半导体产品是否适合客户的应用和计划的产品，以及客户的第三方客户的计划应用和使用，是客户的全部责任。 客户应提供适当的设计和操作保护措施，以尽量减少与其应用程序和产品相关的风险。

大唐恩智浦半导体不承担与基于客户应用程序或产品或客户第三方客户的应用程序或使用中的任何弱点或违约而导致的任何违约、损害、成本或问题相关的任何责任。 客户有责任对使用大唐恩智浦半导体产品的客户的应用程序和产品进行所有必要的测试，以避免应用程序和产品或客户的第三方客户的应用程序或使用出现违约。大唐恩智浦对此不承担任何责任。



出口管制 — 本文件以及此处描述的项目可能受出口管制法规的约束。 出口可能需要国家当局的事先授权。



14.2 商标

注意：所有提及的品牌、产品名称、服务名称和商标均为其各自所有者的财产。

































DNB110xB 数据手册	54 / 54