#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MD文件转DOCX格式转换器
功能：将指定文件夹中的所有Markdown文件转换为Word文档格式
作者：AI助手
日期：2025-08-09
"""

import os
import sys
import glob
from pathlib import Path
import markdown
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import re
from bs4 import BeautifulSoup

def install_required_packages():
    """
    安装必需的Python包
    """
    required_packages = ['python-docx', 'markdown', 'beautifulsoup4']
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            print(f"正在安装 {package}...")
            os.system(f"pip install {package}")

def markdown_to_docx(md_file_path, output_dir=None):
    """
    将单个Markdown文件转换为DOCX格式
    
    参数:
        md_file_path (str): Markdown文件路径
        output_dir (str): 输出目录，默认为None（与源文件同目录）
    
    返回:
        str: 生成的DOCX文件路径
    """
    # 读取Markdown文件
    with open(md_file_path, 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # 将Markdown转换为HTML
    html = markdown.markdown(md_content, extensions=['tables', 'fenced_code'])
    
    # 使用BeautifulSoup解析HTML
    soup = BeautifulSoup(html, 'html.parser')
    
    # 创建Word文档
    doc = Document()
    
    # 处理HTML元素并添加到Word文档
    for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol', 'table', 'pre', 'code']):
        if element.name.startswith('h'):
            # 处理标题
            level = int(element.name[1])
            heading = doc.add_heading(element.get_text().strip(), level=level)
        
        elif element.name == 'p':
            # 处理段落
            paragraph = doc.add_paragraph()
            _add_text_with_formatting(paragraph, element)
        
        elif element.name in ['ul', 'ol']:
            # 处理列表
            _process_list(doc, element)
        
        elif element.name == 'table':
            # 处理表格
            _process_table(doc, element)
        
        elif element.name in ['pre', 'code']:
            # 处理代码块
            code_text = element.get_text()
            paragraph = doc.add_paragraph(code_text)
            paragraph.style = 'No Spacing'
    
    # 确定输出文件路径
    if output_dir is None:
        output_dir = os.path.dirname(md_file_path)
    
    # 生成输出文件名
    base_name = os.path.splitext(os.path.basename(md_file_path))[0]
    output_path = os.path.join(output_dir, f"{base_name}.docx")
    
    # 保存文档
    doc.save(output_path)
    return output_path

def _add_text_with_formatting(paragraph, element):
    """
    向段落添加带格式的文本
    """
    for content in element.contents:
        if hasattr(content, 'name'):
            if content.name == 'strong' or content.name == 'b':
                run = paragraph.add_run(content.get_text())
                run.bold = True
            elif content.name == 'em' or content.name == 'i':
                run = paragraph.add_run(content.get_text())
                run.italic = True
            elif content.name == 'code':
                run = paragraph.add_run(content.get_text())
                run.font.name = 'Courier New'
            else:
                paragraph.add_run(content.get_text())
        else:
            paragraph.add_run(str(content))

def _process_list(doc, list_element):
    """
    处理列表元素
    """
    for li in list_element.find_all('li', recursive=False):
        paragraph = doc.add_paragraph(li.get_text().strip())
        if list_element.name == 'ul':
            paragraph.style = 'List Bullet'
        else:
            paragraph.style = 'List Number'

def _process_table(doc, table_element):
    """
    处理表格元素
    """
    rows = table_element.find_all('tr')
    if not rows:
        return
    
    # 计算列数
    max_cols = max(len(row.find_all(['td', 'th'])) for row in rows)
    
    # 创建表格
    table = doc.add_table(rows=len(rows), cols=max_cols)
    table.style = 'Table Grid'
    
    # 填充表格数据
    for i, row in enumerate(rows):
        cells = row.find_all(['td', 'th'])
        for j, cell in enumerate(cells):
            if j < max_cols:
                table.cell(i, j).text = cell.get_text().strip()

def convert_all_md_files(directory_path):
    """
    转换指定目录中的所有Markdown文件
    
    参数:
        directory_path (str): 包含MD文件的目录路径
    
    返回:
        list: 成功转换的文件列表
    """
    # 确保目录存在
    if not os.path.exists(directory_path):
        print(f"错误：目录 {directory_path} 不存在")
        return []
    
    # 查找所有MD文件
    md_files = glob.glob(os.path.join(directory_path, "*.md"))
    
    if not md_files:
        print(f"在目录 {directory_path} 中没有找到MD文件")
        return []
    
    print(f"找到 {len(md_files)} 个MD文件")
    
    converted_files = []
    
    # 逐个转换文件
    for md_file in md_files:
        try:
            print(f"正在转换: {os.path.basename(md_file)}")
            output_path = markdown_to_docx(md_file)
            converted_files.append(output_path)
            print(f"成功转换为: {os.path.basename(output_path)}")
        except Exception as e:
            print(f"转换文件 {md_file} 时出错: {str(e)}")
    
    return converted_files

def main():
    """
    主函数
    """
    print("MD文件转DOCX格式转换器")
    print("=" * 50)
    
    # 安装必需的包
    print("检查并安装必需的Python包...")
    install_required_packages()
    
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    print(f"当前工作目录: {current_dir}")
    print("开始转换MD文件...")
    
    # 转换所有MD文件
    converted_files = convert_all_md_files(current_dir)
    
    print("\n" + "=" * 50)
    print("转换完成！")
    print(f"成功转换了 {len(converted_files)} 个文件:")
    
    for file_path in converted_files:
        print(f"  - {os.path.basename(file_path)}")

if __name__ == "__main__":
    main()
