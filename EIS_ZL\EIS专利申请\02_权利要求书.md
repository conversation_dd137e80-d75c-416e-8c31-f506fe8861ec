# 权利要求书

## 权利要求1（独立权利要求）

一种基于电化学阻抗谱的电池一致性筛选设备，其特征在于，包括：

DNB1101BB电化学阻抗测试芯片，用于执行电池的电化学阻抗谱测量，所述DNB1101BB芯片支持0.0075Hz至7800Hz的频率范围测量；

STM32主控制器，与所述DNB1101BB芯片通过SPI接口连接，用于控制测试流程和处理测试数据；

外部电流源电路，包括MOSFET开关和功率电阻，通过所述DNB1101BB芯片的VSW引脚驱动，用于在设定频率下向被测电池施加激励电流；

数据处理模块，用于从所述DNB1101BB芯片获取复阻抗数据，包括阻抗实部和虚部；

参数提取模块，用于从所述复阻抗数据中提取多维阻抗参数，包括欧姆阻抗、极化阻抗、SEI膜阻抗和瓦尔堡阻抗；

分组决策模块，基于所述欧姆阻抗和极化阻抗进行电池分组，将电池分为九个一致性等级。

## 权利要求2（从属权利要求）

根据权利要求1所述的电池一致性筛选设备，其特征在于，所述DNB1101BB芯片的技术参数包括：

电压测量范围为1.9V至5.5V，测量精度为±2mV；

温度测量精度为±2.5K，集成双温度传感器；

阻抗测量支持1x、4x、16x三档增益设置；

复阻抗数据格式为16位实部和16位虚部，带4位指数表示。

## 权利要求3（从属权利要求）

根据权利要求1所述的电池一致性筛选设备，其特征在于，所述外部电流源电路包括：

功率电阻，阻值可选择20Ω、10Ω、6.67Ω、5Ω中的一种，用于设定激励电流大小；

MOSFET开关，由所述DNB1101BB芯片的VSW引脚驱动；

保护电路，通过所述DNB1101BB芯片的VDR引脚监控MOSFET的漏极电压状态。

## 权利要求4（从属权利要求）

根据权利要求1所述的电池一致性筛选设备，其特征在于，所述参数提取模块采用以下方法提取阻抗参数：

欧姆阻抗：从高频区域的奈奎斯特曲线实轴截距提取；

极化阻抗：从中频区域的奈奎斯特曲线半圆直径提取；

SEI膜阻抗：从高频区域的特征阻抗值提取；

瓦尔堡阻抗：从低频区域的扩散阻抗特征提取。

## 权利要求5（从属权利要求）

根据权利要求1所述的电池一致性筛选设备，其特征在于，所述分组决策模块采用以下分组策略：

将欧姆阻抗按统计分布分为低、中、高三个档位；

将极化阻抗按统计分布分为低、中、高三个档位；

将欧姆阻抗档位和极化阻抗档位组合，形成九个分组类别：1-1、1-2、1-3、2-1、2-2、2-3、3-1、3-2、3-3。

## 权利要求6（从属权利要求）

根据权利要求1所述的电池一致性筛选设备，其特征在于，还包括：

串口显示屏，与所述STM32主控制器连接，用于实时显示测试状态和结果；

PC上位机，通过USB或串口与所述STM32主控制器通信，采用Modbus RTU协议进行数据传输。

## 权利要求7（从属权利要求）

根据权利要求6所述的电池一致性筛选设备，其特征在于，所述Modbus RTU协议包括以下指令：

频率设置指令：地址4200H～427FH，支持0.001Hz至7813.000Hz的3位小数精度设置；

增益设置指令：地址4280H～42BFH，支持1x、4x、16x增益选择；

测量启动指令：地址0000H～003FH，用于启动阻抗测量；

数据获取指令：地址3000H～31FFH，用于获取64位有符号5位小数的复阻抗数据。

## 权利要求8（独立权利要求）

一种基于电化学阻抗谱的电池一致性筛选方法，其特征在于，包括以下步骤：

S1：设置测试参数，包括频率序列、增益参数和采样电阻；

S2：通过外部电流源在设定频率下向被测电池施加激励电流；

S3：使用DNB1101BB芯片测量电池在各频率下的复阻抗，获取阻抗实部和虚部数据；

S4：基于复阻抗数据绘制奈奎斯特曲线；

S5：从奈奎斯特曲线中提取多维阻抗参数，包括欧姆阻抗、极化阻抗、SEI膜阻抗和瓦尔堡阻抗；

S6：基于欧姆阻抗和极化阻抗的统计分布，将电池分为九个一致性等级。

## 权利要求9（从属权利要求）

根据权利要求8所述的电池一致性筛选方法，其特征在于，所述步骤S1中的频率序列设置采用以下公式：

f = M × k × 2^E

其中，f为测试频率，M为尾数（1-255），k为常数7.4506mHz，E为指数（0-15）。

## 权利要求10（从属权利要求）

根据权利要求8所述的电池一致性筛选方法，其特征在于，所述步骤S6中的分组方法包括：

计算批量电池的欧姆阻抗中位值和标准差；

计算批量电池的极化阻抗中位值和标准差；

基于中位值±0.5倍标准差确定档位阈值；

将每个电池的欧姆阻抗和极化阻抗分别分为低、中、高三档；

组合形成九个分组类别。

## 权利要求11（从属权利要求）

根据权利要求8所述的电池一致性筛选方法，其特征在于，所述方法适用于以下电池类型：

磷酸铁锂电池；

三元锂电池；

钠离子电池；

其他新材料电池。

## 权利要求12（从属权利要求）

根据权利要求8所述的电池一致性筛选方法，其特征在于，所述步骤S5中的参数提取采用等效电路模型拟合，等效电路模型为：

Rs + (Rp || CPE) + Warburg

其中，Rs为欧姆阻抗，Rp为极化阻抗，CPE为常相位元件，Warburg为瓦尔堡阻抗。

---

## 权利要求书说明

### 权利要求结构：
- **独立权利要求1**：设备的整体技术方案
- **从属权利要求2-7**：设备的具体技术特征
- **独立权利要求8**：方法的整体技术方案  
- **从属权利要求9-12**：方法的具体技术特征

### 保护范围：
1. **设备保护**：硬件结构、电路连接、模块功能
2. **方法保护**：测试流程、算法步骤、分组策略
3. **应用保护**：适用于各种电池材料类型

### 技术特点：
1. **核心创新**：EIS全频段测试替代传统1000Hz单频测试
2. **多维分析**：提取多种阻抗参数而非单一参数
3. **智能分组**：九档精确分组提升一致性
4. **通用适用**：适用于所有电池材料类型

这个权利要求书涵盖了您发明的核心技术要点，形成了完整的保护体系。
