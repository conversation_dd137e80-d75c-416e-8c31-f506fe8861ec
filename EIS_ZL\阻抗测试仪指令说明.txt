设置ZM频率(丢弃)
地址范围	4000H～403FH(通道1~64),地址步进1
群发地址	4F0OH
数据类型	16位无符号数，高字节在前，范围：1~7813HZ
读取1~3通道示例
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	寄存器
数量高 8位	寄存器
数量低
8位	CRCH	CRCL	获取CH1~CH3的数据
01H	03H	40H	00H	00H	03H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1 高字节	数 据 1
低字节	数 据 2 高字节	数 据 2 低字节	数 据 3 高字节	数 据 3 低字节	CRCH	CRCL	CH1=1234.5Hz=3039H,
CH2=2345.6Hz=5BAOH,
CH3=3456.7Hz=8707H
01H	03H	06H	30H	39H	5BH	AOH	87H	07H	xxH	xxH	
设置1个通道示例(使用群发地址代替起始地址可以设置所有通道)
地址	命令	起始地 址 高 8
位	起始地
址 低 8
位	数据高 8位	数据低
8位	CRCH	CRCL	
01H	06H	40H	00H	30H	39H	xxH	xxh	
返回帧
01H	06H	40H	00H	1EH	85H	xxH	xxh	
设置1~3多个通道示例
地址	命令	起始地 址 高 8
位	起始地
址 低 8
位	寄存器
数量高
8位	寄存器
数量低
8位	字节数	数 据 1
高字节	数 据 1
低字节	数 据 2
高字节	数 据 2
低字节	数 据 3
高字节	数 据 3
低字节	CRCH
01H	10H	40H	00H	00H	03H	06H	30H	39H	5BH	AOH	87H	07H	xxH
CRCL	
xxH	
返回帧
地址	命令	起始地
址 高 8
位	起始地
址 低 8 位	寄存器
数量高
8位	寄存器
数量低 8位	字节数	CRCH	CRCL	
01H	10H	40H	00H	00H	03H	06H	xxH	xxH	
备注：本指令被下面支持浮点数的新指令替代，后续版将丢弃不能使用.

设置ZM频率(3位小数)
地址范围	4200H～427FH(通道1~64),地址步进2
群发地址	4F07H～4F08H
数据类型	32位3位小数无符号定点数，高字节在前，范围：0.001~7813.000HZ
读 取 1 通 道 示 例
地址	命令	起始地 址 高 8
位	起始地 址 低 8
位	寄存器 数量高 8位	寄存器
数量低 8位	CRCH	CRCL	获取CH1的数据
01H	03H	42H	0OH	0OH	02H	xxH	xxH	
返回帧
地址	命令	返回字 节数	数 据 1
高字节	数 据 1 低字节	数 据 2 高字节	数 据 2
低字节	CRCH	CRCL			CH1=1234.567Hz=12D687H,
01H	03H	04H	00H	12H	D6H	87H	xxH	xxH			
设置所有通道示例
地址	命令	起始地
址 高 8
位	起始地 址 低 8 位	寄存器
数量高
8位	寄存器 数量低
8位	字节数	数 据 1 高字节	数 据 1
低字节	数 据 2
高字节	数 据 2 低字节	CRCH	CRCL	
01H	10H	4FH	07H	00H	02H	04H	00H	12H	D6H	87H	xxH	xxH	
			1234.567Hz=12D687H,
			
返回帧
地址	命令	起始地
址 高 8
位	起始地
址 低 8
位	寄存器 数量高
8位	寄存器 数量低
8位	字节数	CRCH	CRCL	
01H	10H	43H	00H	00H	04H	08H	xxH	xxH	
设置1~2多个通道示例
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	寄存器 数量高
8位	寄存器 数量低 8位	字节数	数 据 1 高字节	数 据 1
低字节	数 据 2
高字节	数 据 2 低字节	数 据 3
高字节	数 据 3 低字节	数据4高 字节
01H	10H	42H	00H	00H	04H	08H	00H	12H	D6H	87H	00H	23H	CAH
数据4  低  字
节	CRCH	CRCL	CH1=1234.567Hz=12D687H,
CH2=2345.678Hz=23CACEH,
CEH	xxH	xxH	
返回帧
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	寄存器 数量高
8位	寄存器 数量低 8位	字节数	CRCH	CRCL	
01H	10H	43H	00H	00H	04H	08H	xxH	xxH	


设置ZM增益
地址范围	4280H~42BFH  (通道1~64),地址步进1
群发地址	4F09H
数据类型	16位无符号整型，高字节在前，范围：1,4,16
读取1~3通道示例
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	寄存器 数量高 8位	寄存器
数量低
8位	CRCH	CRCL	CH1=1
CH2=4
CH3=16
01H	03H	42H	80H	00H	03H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1 高字节	数 据 1
低字节	数 据 2
高字节	数 据 2 低字节	数 据 3
高字节	数 据 3
低字节	CRCH	CRCL	
01H	03H	06H	00H	01H	00H	04H	00H	10H	xxH	xxH	
设置1个通道示例(使用群发地址代替起始地址可以设置所有通道)
地址	命令	起始地 址 高 8
位	起始地
址 低 8
位	数据高 8位	数据低
8位	CRCH	CRCL	
01H	06H	42H	80H	00H	04H	xxH	xxh	
返回帧
01H	06H	42H	80H	00H	04H	xxH	xxh	
设置1~3多个通道示例
地址	命令	起始地 址 高 8
位	起始地
址 低 8
位	寄存器
数量高
8位	寄存器 数量低
8位	字节数	数 据 1
高字节	数 据 1
低字节	数 据 2 高字节	数 据 2
低字节	数 据 3
高字节	数 据 3
低字节	CRCH
01H	10H	42H	80H	00H	03H	06H	00H	01H	00H	04H	00H	10H	xxH
CRCL	
xxH	
返回帧
地址	命令	起始地
址 高 8
位	起始地 址 低 8 位	寄存器
数量高
8位	寄存器 数量低 8位	字节数	CRCH	CRCL	
01H	10H	42H	80H	00H	03H	06H	xxH	xxH	


设置ZM平均次数
地址范围	4040H～407FH(通道1~64),地址步进1
群发地址	4F01H
数据类型	16位无符号整型，高字节在前，范围：1~64
读取1~3通道示例
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	寄存器 数量高 8位	寄存器
数量低
8位	CRCH	CRCL	
01H	03H	40H	40H	00H	03H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1 高字节	数 据 1
低字节	数 据 2
高字节	数 据 2 低字节	数 据 3
高字节	数 据 3
低字节	CRCH	CRCL	
01H	03H	06H	00H	08H	00H	20H	00H	40H	xxH	xxH	
设置1个通道示例(使用群发地址代替起始地址可以设置所有通道)
地址	命令	起始地 址 高 8
位	起始地
址 低 8
位	数据高 8位	数据低
8位	CRCH	CRCL	
01H	06H	40H	40H	00H	08H	xxH	xxh	
返回帧
01H	06H	40H	40H	00H	08H	xxH	xxh	
设置1~3多个通道示例
地址	命令	起始地 址 高 8
位	起始地
址 低 8
位	寄存器
数量高
8位	寄存器 数量低
8位	字节数	数 据 1
高字节	数 据 1
低字节	数 据 2 高字节	数 据 2
低字节	数 据 3
高字节	数 据 3
低字节	CRCH
01H	10H	40H	40H	00H	03H	06H	00H	08H	00H	20H	00H	40H	xxH
CRCL	
xxH	
返回帧
地址	命令	起始地
址 高 8
位	起始地 址 低 8 位	寄存器 数量高
8位	寄存器 数量低 8位	字节数	CRCH	CRCL	
01H	10H	40H	40H	00H	03H	06H	xxH	xxH	


设置ZM循环次数(丢弃)
地址范围	4080H～40BFH(通道1～64),地址步进1
群发地址	4F02H
数据类型	16位无符号整型，高字节在前，范围：1～64
读取1~3通道示例
地址	命令	起始地 址 高 8
位	起始地 址 低 8
位	寄存器 数量高 8位	寄存器 数量低
8位	CRCH	CRCL	
01H	03H	40H	80H	00H	03H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1 高字节	数 据 1
低字节	数 据 2 高字节	数 据 2 低字节	数 据 3 高字节	数 据 3
低字节	CRCH	CRCL	
01H	03H	06H	00H	08H	00H	20H	00H	40H	xxH	xxH	
设置1个通道示例(使用群发地址代替起始地址可以设置所有通道)
地址	命令	起始地 址 高 8
位	起始地
址 低 8
位	数据高 8位	数据低
8位	CRCH	CRCL	
01H	06H	40H	80H	00H	08H	xxH	xxh	
返回帧
01H	06H	40H	80H	00H	08H	xxH	xxh	
设置1~3多个通道示例
地址	命令	起始地 址 高 8
位	起始地
址 低 8
位	寄存器
数量高
8位	寄存器
数量低
8位	字节数	数 据 1
高字节	数 据 1
低字节	数 据 2
高字节	数 据 2
低字节	数 据 3
高字节	数 据 3
低字节	CRCH
01H	10H	40H	80H	00H	03H	06H	00H	08H	00H	20H	00H	40H	xxH
CRCL	
xxH	
返回帧
地址	命令	起始地
址 高 8
位	起始地
址 低 8 位	寄存器 数量高
8位	寄存器
数量低 8位	字节数	CRCH	CRCL	
01H	10H	40H	80H	00H	03H	06H	xxH	xxH	
备注：该指令没有实际用途，后续版本将丢弃不使用.

选择ZM采样电阻
地址范围	40COH～40FFH(通道1～64),地址步进1
群发地址	4F03H
数据类型	16位无符号整型，高字节在前，范围：0～3(0-20R,1-10R,2-6.67R,3-5R)
读取1~3通道示例
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	寄存器 数量高 8位	寄存器
数量低
8位	CRCH	CRCL	
01H	03H	40H	COH	00H	03H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1 高字节	数 据 1
低字节	数 据 2
高字节	数 据 2 低字节	数 据 3
高字节	数 据 3
低字节	CRCH	CRCL	
01H	03H	06H	00H	00H	00H	01H	00H	02H	xxH	xxH	
设置1个通道示例(使用群发地址代替起始地址可以设置所有通道)
地址	命令	起始地 址 高 8
位	起始地
址 低 8
位	数据高 8位	数据低
8位	CRCH	CRCL	
01H	06H	40H	COH	00H	01H	xxH	xxh	
返回帧
01H	06H	40H	COH	00H	01H	xxH	xxh	
设置1~3多个通道示例
地址	命令	起始地 址 高 8
位	起始地
址 低 8
位	寄存器
数量高
8位	寄存器 数量低
8位	字节数	数 据 1
高字节	数 据 1
低字节	数 据 2 高字节	数 据 2
低字节	数 据 3
高字节	数 据 3
低字节	CRCH
01H	10H	40H	COH	00H	03H	06H	00H	00H	00H	01H	00H	02H	xxH
CRCL	
xxH	
返回帧
地址	命令	起始地
址 高 8
位	起始地 址 低 8 位	寄存器 数量高
8位	寄存器 数量低 8位	字节数	CRCH	CRCL	
01H	10H	40H	40H	00H	03H	06H	xxH	xxH	


均衡电压设置(基于电压的均衡模式)
地址范围	4100H～413FH(  通道1~64),地址步进1
群发地址	4F04H
数据类型	16位无符号整型，高字节在前，范围：0～255;0=1200(mV),1=1218.8(mV),1LSB=18.8(mV)
备注	该模式运行时受电压和时间同时控制，两者先到为准
读取1~3通道示例
地址	命令	起始地
址 高 8 位	起始地
址 低 8
位	寄存器
数量高
8位	寄存器
数量低
8位	CRCH	CRCL	
01H	03H	41H	00H	00H	03H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1
高字节	数 据 1
低字节	数 据 2
高字节	数 据 2
低字节	数 据 3
高字节	数 据 3 低字节	CRCH	CRCL	
01H	03H	06H	00H	C5H	00H	C5H	00H	C5H	xxXH	xxH	
设置1个通道示例(使用群发地址代替起始地址可以设置所有通道)
地址	命令	起始地 址 高 8
位	起始地 址 低 8
位	数据高 8位	数据低
8位	CRCH	CRCL	
01H	06H	41H	00H	00H	C5H	xxH	xxh	
返回帧
01H	06H	41H	00H	00H	C5H	xxH	xxh	
设置1~3多个通道示例
地址	命令	起始地
址 高 8
位	起始地
址 低 8
位	寄存器
数量高 8位	寄存器
数量低
8位	字节数	数 据 1
高字节	数 据 1
低字节	数 据 2
高字节	数 据 2
低字节	数 据 3
高字节	数 据 3 低字节	CRCH
01H	10H	41H	00H	00H	03H	06H	00H	C5H	00H	C5H	00H	C5H	xxH
CRCL	
xxH	
返回帧
地址	命令	起始地 址 高 8
位	起始地 址 低 8 位	寄存器
数量高
8位	寄存器
数量低 8位	字节数	CRCH	CRCL	
01H	10H	41H	00H	00H	03H	06H	xxH	xxH	


均衡时间设置(基于时间的均衡模式)
地址范围	4140H～417FH(通道1～64),地址步进1
群发地址	4F05H
数据类型	16位无符号整型，高字节在前，范围：0～255;1LSB=134(S),最大9.5小时
读取1~3通道示例
地址	命令	起始地 址 高 8
位	起始地 址 低 8
位	寄存器 数量高 8位	寄存器
数量低
8位	CRCH	CRCL	
01H	03H	41H	40H	00H	03H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1
高字节	数 据 1 低字节	数据2  高字节	数 据 2
低字节	数 据 3 高字节	数据3  低字节	CRCH	CRCL	
01H	03H	06H	00H	10H	00H	20H	00H	30H	xxH	xxH	
设置1个通道示例(使用群发地址代替起始地址可以设置所有通道)
地址	命令	起始地 址 高 8
位	起始地 址 低 8
位	数据高 8位	数据低
8位	CRCH	CRCL	
01H	06H	41H	40H	00H	10H	xxH	xxh	
返回帧
01H	06H	41H	40H	00H	10H	xxH	xxh	
设置1~3多个通道示例
地址	命令	起始地 址 高 8
位	起始地
址 低 8
位	寄存器 数量高 8位	寄存器
数量低 8位	字节数	数 据 1
高字节	数 据 1 低字节	数 据 2 高字节	数 据 2 低字节	数 据 3
高字节	数 据 3 低字节	CRCH
01H	10H	41H	40H	00H	03H	06H	00H	10H	00H	20H	00H	30H	xxH
CRCL	
xxH	
返回帧
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	寄存器
数量高
8位	寄存器
数量低
8位	字节数	CRCH	CRCL	
01H	10H	41H	40H	00H	03H	06H	xxH	xxH	


均衡功能PWM占空比设置
地址范围	4180H～41BFH(通道1～64),地址步进1
群发地址	4F06H
数据类型	16位无符号整型，高字节在前，范围：0～14;0,1=12.5%,2,3=25%.….D,E=100%,1LSB=12.5%,; CURRENT=PWM*(Volt/     采 样 电 阻 )
读取1~3通道示例
地址	命令	起始地
址 高 8
位	起始地
址低8
位	寄存器
数量高
8位	寄存器
数量低
8位	CRCH	CRCL	
01H	03H	41H	80H	00H	03H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1
高字节	数 据 1 低字节	数 据 2 高字节	数 据 2 低字节	数 据 3
高字节	数 据 3 低字节	CRCH	CRCL	
01H	03H	06H	00H	01H	00H	02H	00H	03H	xxH	xxH	
设置1个通道示例(使用群发地址代替起始地址可以设置所有通道)
地址	命令	起始地
址 高 8
位	起始地
址 低 8
位	数据高 8位	数据低 8位	CRCH	CRCL	
01H	06H	41H	80H	00H	01H	xxH	xxh	
返回帧
01H	06H	41H	80H	00H	01H	xxH	xxh	
设置1~3多个通道示例
地址	命令	起始地 址 高 8
位	起始地 址低8
位	寄存器
数量高
8位	寄存器
数量低
8位	字节数	数 据 1 高字节	数 据 1 低字节	数 据 2
高字节	数 据 2
低字节	数 据 3
高字节	数 据 3
低字节	CRCH
01H	10H	41H	80H	00H	03H	06H	00H	01H	00H	02H	00H	03H	xxH
CRCL	
xxH	
返回帧
地址	命令	起始地
址 高 8
位	起始地
址 低 8
位	寄存器
数量高
8位	寄存器
数量低 8位	字节数	CRCH	CRCL	
01H	10H	41H	80H	00H	03H	06H	xxH	xxH	


启动阻抗测量
地址范围	0000H～003FH(通道1～64),地址步进1
群发地址	0FOOH
数据类型	1Bit;范围：0～1,写1启动阻抗测量，写1后一直保持直到测量完成或错误停止后清0,也可以写0停止当 前测量过程
读取1~12通道示例
地址	命令	起始地
址 高 8 位	起始地 址 低 8
位	寄存器 数量高 8位	寄存器
数量低
8位	CRCH	CRCL	CH1=1,CH2=0,CH3=1,CH4=0,CH5=1,CH6=0,CH7=1, CH8=0,CH9=1,CH10=0,CH11=1,CH12=0
01H	01H	00H	00H	00H	OCH	xxH	xxH	
返回帧
地址	命令	返回字
节数	数据1	数据2	CRCH	CRCL	
01H	01H	02H	55H	05H	xxH	xxH	
设置1个通道示例(使用群发地址代替起始地址可以设置所有通道)
地址	命令	起始地
址 高 8
位	起始地
址 低 8
位	数据高
8位	数据低
8位	CRCH	CRCL	向CH1写入1启动阻抗测试
写1=FFOOH
写0=0000H
01H	05H	00H	00H	FFH	00H	xxH	xxh	
返回帧
01H	05H	00H	00H	FFH	00H	xxH	xxh	
设置1~12多个通道示例
地址	命令	起始地
址 高 8
位	起始地 址 低 8 位	寄存器
数量高
8位	寄存器
数量低 8位	字节数	数据1	数据2	CRCH	CRCL	
01H	OFH	00H	00H	00H	OCH	02H	55H	05H	xxH	xxH	
返回帧
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	寄存器 数量高
8位	寄存器
数量低 8位	字节数	CRCH	CRCL	
01H	OFH	00H	00H	00H	OCH	02H	xxH	xxH	


启动均衡功能
地址范围	0040H～007FH(通道1～64),地址步进1
群发地址	0F01H
数据类型	1Bit;范围：0～1,写1启动阻抗测量，写1后一直保持直到测量完成或错误停止后清0,也可以写0停止当 前平衡过程
读取1~12通道示例
地址	命令	起始地
址 高 8 位	起始地 址 低 8
位	寄存器 数量高 8位	寄存器
数量低
8位	CRCH	CRCL	CH1=1,CH2=0,CH3=1,CH4=0,CH5=1,CH6=0,CH7=1, CH8=0,CH9=1,CH10=0,CH11=1,CH12=0
01H	01H	00H	40H	00H	OCH	xxH	xxH	
返回帧
地址	命令	返回字
节数	数据1	数据2	CRCH	CRCL	
01H	01H	02H	55H	05H	xxH	xxH	
设置1个通道示例(使用群发地址代替起始地址可以设置所有通道)
地址	命令	起始地
址 高 8
位	起始地
址 低 8
位	数据高
8位	数据低
8位	CRCH	CRCL	向CH1写入1启动阻抗测试
写1=FFOOH
写0=0000H
01H	05H	00H	40H	FFH	00H	xxH	xxh	
返回帧
01H	05H	00H	40H	FFH	00H	xxH	xxh	
设置1~12多个通道示例
地址	命令	起始地
址 高 8
位	起始地 址 低 8 位	寄存器
数量高
8位	寄存器
数量低 8位	字节数	数据1	数据2	CRCH	CRCL	
01H	OFH	00H	40H	00H	OCH	02H	55H	05H	xxH	xxH	
返回帧
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	寄存器 数量高
8位	寄存器
数量低 8位	字节数	CRCH	CRCL	
01H	OFH	00H	40H	00H	OCH	02H	xxH	xxH	


设置均衡模式
地址范围	0080H～007FH(通道1～64),地址步进1
群发地址	0F02H
数据类型	1Bit;范围：0～1,0-基于时间的平衡模式，1-基于电压的平衡模式
读取1~12通道示例
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	寄存器
数量高 8位	寄存器
数量低
8位	CRCH	CRCL	CH1=1,CH2=0,CH3=1,CH4=0,CH5=1,CH6=0,CH7=1,
CH8=0,CH9=1,CH10=0,CH11=1,CH12=0
01H	01H	00H	80H	00H	OCH	xxH	xxH	
返回帧
地址	命令	返回字
节数	数据1	数据2	CRCH	CRCL	
01H	01H	02H	55H	05H	xxH	xxH	
设置1个通道示例(使用群发地址代替起始地址可以设置所有通道)
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	数据高 8位	数据低 8位	CRCH	CRCL	向CH1写入1启动阻抗测试
写1=FFOOH
写0=0000H
01H	05H	00H	80H	FFH	00H	xxH	xxh	
返回帧
01H	05H	00H	80H	FFH	00H	xxH	xxh	
设置1~12多个通道示例
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	寄存器
数量高 8位	寄存器
数量低 8位	字节数	数据1	数据2	CRCH	CRCL	
01H	OFH	00H	80H	00H	OCH	02H	55H	05H	xxH	xxH	
返回帧
地址	命令	起始地
址 高 8
位	起始地
址 低 8
位	寄存器
数量高 8位	寄存器
数量低 8位	字节数	CRCH	CRCL	
01H	OFH	00H	80H	00H	OCH	02H	xxH	xxH	


ZM测量期间数据更新标志
地址范围	1000H～103FH(通道1～64),地址步进1
群发地址	
数据类型	1Bit;范围：0～1,ZM工作期间阻抗数据更新后该标志位置1,读取RE和IM数据后自动清0
读取1~12通道示例
地址	命令	起始地
址 高 8
位	起始地
址 低 8
位	寄存器
数量高 8位	寄存器
数量低
8位	CRCH	CRCL	CH1=1,CH2=0,CH3=1,CH4=0,CH5=1,CH6=0,CH7=1, CH8=0,CH9=1,CH10=0,CH11=1,CH12=0
01H	02H	00H	80H	00H	OCH	xxH	xxH	
返回帧
地址	命令	返回字 节数	数据1	数据2	CRCH	CRCL	
01H	02H	02H	55H	05H	xxH	xXH	


获取RE阻抗数据
地址范围	3000H～307FH(通道1~32),地址步进4
群发地址	
数据类型	64位有符号定点5位小数，数据格式(HG,FE,DC,BA)
读取1~2通道示例
地址	命令	起始地
址 高 8 位	起始地
址低8
位	寄存器 数量高 8位	寄存器
数量低
8位	CRCH	CRCL	0.00001=0000000000000001H  -0.00001=FFFFFFFFFFFFFFFFH
01H	04H	30H	00H	00H	08H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数据1
高 8 位	数 据 1
低 8 位	数 据 2 高 8 位	数 据 2
低 8 位	数 据 3 高8位	数 据 3
低 8 位	数 据 4 高8位	数 据 4
低8位	数 据 5
高8位	数 据 5
低 8 位	数据6高 8位
01H	04H	10H	00H	00H	00H	00H	49H	96H	02H	D2H	FFH	FFH	FFH
数据6
低8位	数 据 7
高 8 位	数 据 7 低 8 位	数 据 8
高8位	数 据 8 低 8 位	CRCH	CRCL	CH1=12345.67890=499602D2H
CH2=-99999.78900=FFFFFFFDABF46E6CH
FDH	ABH	F4H	6EH	6CH	xxH	xxH	


获取IM阻抗数据
地址范围	3080H～30FFH(通道1~32),地址步进4
群发地址	
数据类型	64位有符号定点5位小数，数据格式(HG,FE,DC,BA)
读取1~2通道示例
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	寄存器 数量高 8位	寄存器
数量低 8位	CRCH	CRCL	0.00001=0000000000000001H  -0.00001=FFFFFFFFFFFFFFFFH
01H	04H	30H	80H	00H	08H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1 高 8 位	数 据 1
低 8 位	数 据 2
高 8 位	数据2  低 8 位	数 据 3
高 8 位	数 据 3 低 8 位	数 据 4 高 8 位	数 据 4
低 8 位	数 据 5 高 8 位	数 据 5
低 8 位	数据6高
8位
01H	04H	10H	00H	00H	00H	00H	49H	96H	02H	D2H	FFH	FFH	FFH
数据6  低 8 位	数 据 7
高8位	数 据 7
低8位	数 据 8
高 8 位	数 据 8
低 8 位	CRCH	CRCL	CH1=12345.67890=499602D2H
CH2=-99999.78900=FFFFFFFDABF46E6CH
FDH	ABH	F4H	6EH	6CH	xxH	xxH	


获取Zreal数据
地址范围	3100H～317FH(通道1～32),地址步进4
群发地址	
数据类型	64位有符号定点5位小数，数据格式(HG,FE,DC,BA)
读取1~2通道示例
地址	命令	起始地
址 高 8
位	起始地
址 低 8
位	寄存器
数量高
8位	寄存器 数量低
8位	CRCH	CRCL	0.00001=0000000000000001H  -0.00001=FFFFFFFFFFFFFFFFH
01H	04H	31H	00H	00H	08H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1 高8位	数 据 1 低 8 位	数 据 2
高 8 位	数 据 2 低 8 位	数 据 3
高 8 位	数据3  低 8 位	数 据 4 高 8 位	数 据 4
低 8 位	数 据 5
高 8 位	数据5
低 8 位	数 据 6
高8位
01H	04H	10H	0OH	00H	0OH	00H	49H	96H	02H	D2H	FFH	FFH	FFH
数据6
低 8 位	数据7
高8位	数 据 7 低8位	数据8
高8位	数 据 8 低 8 位	CRCH	CRCL	CH1=12345.67890=499602D2H
CH2=-99999.78900=FFFFFFFDABF46E6CH
FDH	ABH	F4H	6EH	6CH	xxH	xxH	


获取Zimag数据
地址范围	3180H～31FFH(通道1～32),地址步进4
群发地址	
数据类型	64位有符号定点5位小数，数据格式(HG,FE,DC,BA)
读取1~2通道示例
地址	命令	起始地
址 高 8
位	起始地 址 低 8 位	寄存器 数量高 8位	寄存器
数量低 8位	CRCH	CRCL	0.00001=0000000000000001H  -0.00001=FFFFFFFFFFFFFFFFH
01H	04H	31H	80H	00H	08H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1 高 8 位	数 据 1 低 8 位	数 据 2
高 8 位	数 据 2 低 8 位	数 据 3
高8位	数 据 3 低 8 位	数 据 4 高 8 位	数 据 4
低 8 位	数 据 5
高 8 位	数 据 5 低8位	数 据 6 高 8 位
01H	04H	10H	00H	00H	00H	00H	49H	96H	02H	D2H	FFH	FFH	FFH
数 据 6 低 8 位	数 据 7 高8位	数 据 7
低 8 位	数 据 8 高 8 位	数 据 8
低 8 位	CRCH	CRCL	CH1=12345.67890=499602D2H
CH2=-99999.78900=FFFFFFFDABF46E6CH
FDH	ABH	F4H	6EH	6CH	xxH	xxH	


获取VZM数据
地址范围	3200H～327FH(通道1～64),地址步进2
群发地址	
数据类型	32位无符号定点7位小数，数据格式(DC,BA)
读取1~3通道示例
地址	命令	起始地
址 高 8
位	起始地
址 低 8 位	寄存器 数量高
8位	寄存器
数量低
8位	CRCH	CRCL	0.0000001=00000001H  -0.0000001=FFFFFFFFH
01H	04H	32H	00H	00H	06H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1
高8位	数 据 1
低 8 位	数 据 2
高 8 位	数 据 2
低 8 位	数 据 3
高 8 位	数 据 3
低 8 位	数 据 4
高 8 位	数 据 4
低8位	数 据 5
高 8 位	数 据 5
低 8 位	数 据 6
高 8 位
01H	04H	OCH	02H	5BH	B1H	45H	01H	84H	C7H	2EH	01H	F1H	31H
数据6
低 8 位	CRCH	CRCL					CH1=3.9563589V=25BB145H
CH2=2.5478958V=184C72EH
CH2=3.2584133V=1F131C5H
C5H	xxH	xxH					


获取温度数据
地址范围	3300H(通道1)～333FH(通道1～64),地址步进1
群发地址	
数据类型	16位有符号定点1位小数，数据格式BA, 示 例 数 据32.5C=325=145H
读取1~3通道示例
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	寄存器 数量高
8位	寄存器
数量低 8位	CRCH	CRCL	
01H	04H	33H	00H	00H	03H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1
高 8 位	数 据 1
低 8 位	数 据 2 高8位	数 据 2
低 8 位	数 据 3 高8位	数 据 3
低 8 位	CRCH	CRCL	
01H	04H	06H	01H	45H	01H	45H	01H	45H	xxH	xxH	


获取电池电压数据
地址范围	3340H～337FH(通道1～64),地址步进1
群发地址	
数据类型	16位无符号4位定点小数，数据格式BA, 示 例 数 据4.1234(V)=41234=A112H
读取1~3通道示例
地址	命令	起始地 址 高 8 位	起始地 址 低 8
位	寄存器
数量高
8位	寄存器
数量低
8位	CRCH	CRCL	
01H	04H	33H	40H	00H	03H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1 高8位	数 据 1 低8位	数 据 2
高8位	数 据 2 低8位	数 据 3 高8位	数 据 3
低8位	CRCH	CRCL	CH1=4.0025=9C59H
CH2=3.2586=7F4AH
CH3=3.9878=9BC6H
01H	04H	06H	9CH	59H	7FH	4AH	9BH	C6H	xxH	xxH	


获取通道数量
地址范围	3E0OH
群发地址	
数据类型	16位无符号整型数，数据格式BA
读取通道数量示例
地址	命令	起始地 址 高 8 位	起始地
址 低 8
位	寄存器 数量高 8位	寄存器
数量低 8位	CRCH	CRCL	
01H	04H	3EH	00H	00H	01H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1 高8位	数 据 1 低8位	CRCH	CRCL	
01H	04H	02H	00H	08H	xxH	xxH	


获取软件版本号
地址范围	3E01H,地址步进2
群发地址	
数据类型	16位无符号整型数，数据格式DC,BA(压缩BCD码格式)
读取1~3通道示例
地址	命令	起始地
址 高 8
位	起始地 址 低 8
位	寄存器 数量高 8位	寄存器
数量低 8位	CRCH	CRCL	
01H	04H	3EH	01H	00H	02H	xxH	xxH	
返回帧
地址	命令	返回字
节数	数 据 1 高8位	数 据 1
低8位	数 据 2
高8位	数 据 2
低8位	CRCH	CRCL	
01H	04H	06H	00H	23H	12H	26H	xxH	xxH	


获取状态码
地址范围	3380H～33BFH(通道1～64),地址步进1
群发地址	
数据类型	16位整型数，数据格式BA,状态码查看以下表格
读取1~3通道示例
地址	命令	起始地 址 高 8
位	起始地 址 低 8 位	寄存器 数量高
8位	寄存器
数量低
8位	CRCH	CRCL	
01H	04H	33H	80H	00H	03H	xxH	xxH	
返回帧
地址	命令	返回字 节数	数 据 1
高8位	数 据 1
低 8 位	数 据 2
高8位	数 据 2
低 8 位	数 据 3
高 8 位	数 据 3
低 8 位	CRCH	CRCL			
01H	04H	06H	00H	0OH	00H	00H	00H	00H	xxH	xxH			

状态码说明

0000H	
空闲

0001H	
Z M 测 量 中

0002H	
平衡功能运行中

0003H	
电池电压错误/电池未安装

0004H	
设置错误

0005H	
硬件错误/ADC错误 …

0006H	
测量完成



开始OTA
地址范围	0000H
群发地址	
数据类型	8位无符号整型数，数据格式BA
示例
地址	命令	起始地 址 高 8
位	起始地 址 低 8 位	寄存器 数量高
8位	寄存器
数量低
8位	字节数	数据字节1	数据字节2	数据字节3	数据字节4	数据字节5	数据字节6	数据字节7
02H	10H	00H	00H	00H	08H	10H	xxH	xxH	xxH	xxH	xxH	xxH	xxH
数据字节8	数据字节9	数据字节10	数据字节11	数据字节12	数据字节13	数据字节14	数据字节15	数据字节16	CRCH	CRCL			
xxH	xxH	xxH	xxH	xxH	xxH	xxH	xxH	xxH	xxH	xxH			
返回帧
地址	命令	起始地 址 高 8
位	起始地 址 低 8 位	寄存器 数量高
8位	寄存器
数量低
8位	CRCH	CRCL						
02H	10H	00H	00H	00H	08H	C1H	FCH						



写OTA数据
地址范围	0001H
群发地址	
数据类型	8位无符号整型数，数据格式BA
示例
地址	命令	起始地 址 高 8
位	起始地 址 低 8 位	寄存器 数量高
8位	寄存器
数量低
8位	字节数	地址偏移[0:7]	地址偏移[8:15]	地址偏移[16:23]	地址偏移[24:31]	数据字节1	数据字节2	数据字节3
02H	10H	00H	01H	00H	0AH	14H	xxH	xxH	xxH	xxH	xxH	xxH	xxH
数据字节4	数据字节5	数据字节6	数据字节7	数据字节8	数据字节9	数据字节10	数据字节11	数据字节12	数据字节13	数据字节14	数据字节15	数据字节16	CRCH
xxH	xxH	xxH	xxH	xxH	xxH	xxH	xxH	xxH	xxH	xxH	xxH	xxH	xxH
CRCL													
xxH													
返回帧
地址	命令	起始地 址 高 8
位	起始地 址 低 8 位	寄存器 数量高
8位	寄存器
数量低
8位	CRCH	CRCL						
02H	10H	00H	01H	00H	0AH	xxH	xxH						



结束OTA
地址范围	0002H
群发地址	
数据类型	8位无符号整型数，数据格式BA
示例
地址	命令	起始地 址 高 8
位	起始地 址 低 8 位	寄存器 数量高
8位	寄存器
数量低
8位	CRCH	CRCL						
02H	06H	00H	02H	00H	00H	28H	39H						
返回帧
地址	命令	起始地 址 高 8
位	起始地 址 低 8 位	寄存器 数量高
8位	寄存器
数量低
8位	CRCH	CRCL						
02H	06H	00H	02H	00H	00H	28H	39H						
































读取OTA错误码
地址范围	0003H
群发地址	
数据类型	8位有符号整型数，数据格式BA
示例
地址	命令	起始地 址 高 8
位	起始地 址 低 8 位	寄存器 数量高
8位	寄存器
数量低
8位	CRCH	CRCL						
02H	03H	00H	03H	00H	01H	74H	39H						
返回帧
地址	命令	返回字
节数	固定00	错误码	CRCH	CRCL							
02H	03H	02H	00H	xxH	xxH	xxH							

错误码说明

0	
无错误

-1	
固件大小错误

-2	
固件校验码错误

-3	
地址偏移错误

-4	
写OTA数据时长度错误
